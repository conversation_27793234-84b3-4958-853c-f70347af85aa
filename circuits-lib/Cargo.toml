[package]
name = "circuits-lib"
version = "0.1.0"
edition = "2021"

[dependencies]
sha2 = { workspace = true, default-features = false }
serde = { workspace = true, default-features = false, features = ["derive"] }

risc0-zkvm = { workspace = true, features = ["std"] }
risc0-groth16 = { workspace = true }

borsh = { workspace = true, features = ["derive"] }
bitcoin = { workspace = true, features = ["rand-std", "serde"] }

ark-bn254 = { workspace = true }
ark-ff = { workspace = true }
ark-ec = { workspace = true }
ark-std = { workspace = true }
ark-crypto-primitives = { workspace = true }
ark-serialize = { workspace = true }
ark-relations = { workspace = true }
ark-groth16 = { workspace = true, default-features = false }

num-bigint = { workspace = true }
num-traits = { workspace = true }
hex = { workspace = true }
once_cell = { workspace = true }
hex-literal = { workspace = true }
jmt = { workspace = true }

eyre = { workspace = true }
tracing = { workspace = true }

alloy-primitives = { workspace = true, features = ["serde"] }
alloy-rpc-types = { workspace = true }

serde_json = { workspace = true }
bincode = { workspace = true }
alloy-rpc-types-eth = { workspace = true, features = ["serde"] }
derive_more = { workspace = true, features = ["display"]} 
crypto-bigint = { workspace = true }
blake3 = { workspace = true }
itertools = { workspace = true }
k256 = { workspace = true }
lazy_static = { workspace = true }

citrea-sov-rollup-interface = { workspace = true }

[features]
default = []
use-test-vk = [] # Use the test verification key - this is used for testing purposes only
