# Header Chain Circuit Documentation

This document describes the logic and structure of the header chain circuit, which implements Bitcoin header chain verification logic. This circuit is designed to operate within a zero-knowledge virtual machine (zkVM) environment, with some components also supporting native execution. Its primary purpose is to verify sequences of Bitcoin block headers, ensuring the integrity and continuity of the chain state.

### Merkle Mountain Range (MMR) Implementations

The circuit's core logic is supported by two distinct Merkle Mountain Range (MMR) implementations. The [`mmr_guest.rs`](../circuits-lib/src/header_chain/mmr_guest.rs) module provides an MMR tailored for the constrained environment of a zkVM. This `MMRGuest` implementation is designed to efficiently verify inclusion proofs for block hashes, confirming that a given block header is part of the verified chain without needing to store the entire chain history.

In contrast, the [`mmr_native.rs`](../circuits-lib/src/header_chain/mmr_native.rs) module provides a native MMR implementation for use outside the zkVM. This `MMRNative` is used to build the MMR from a sequence of block headers and generate inclusion proofs. These proofs can then be passed to the `MMRGuest` within the zkVM for verification, bridging the gap between off-chain data preparation and on-chain verification.

### Core Circuit Logic

The central component of the circuit is the main logic module, found in [`mod.rs`](../circuits-lib/src/header_chain/mod.rs). This module orchestrates the entire verification process, from handling input and output to performing all necessary cryptographic and protocol-level checks.

The circuit's entry point function, `header_chain_circuit`, takes a list of block headers as input and applies them to a `ChainState` data structure. This `ChainState` keeps track of essential chain parameters such as block height, accumulated proof of work, and the current difficulty target. For each block header, the circuit performs a series of critical validations:

* **Method ID Consistency**: It first ensures that the `method_id` of the input matches that of any previous proof, preventing the use of different circuit versions.
* **Chain Continuity**: It verifies that each block's `prev_block_hash` matches the `best_block_hash` from the preceding state, guaranteeing a continuous chain.
* **Proof of Work**: It calculates the double SHA256 hash of the block header and checks that it is less than or equal to the current difficulty target.
* **Difficulty Adjustment**: It validates the `bits` field in the header and, at the end of each 2016-block epoch, calculates and validates the new difficulty target based on the time elapsed.
* **Timestamp Validation**: It ensures that the block's timestamp is greater than the median of the previous 11 block timestamps, preventing timestamp manipulation.
* **MMR Proof Verification**: It verifies the integrity of the block header's inclusion proof using the `MMRGuest`.

Upon successful execution, the circuit outputs an updated `ChainState` and a hash of the initial state. This output can then be used as input for a subsequent circuit run, enabling the verification of a continuous, long chain of headers across multiple proofs. The entire process is designed to `panic!` on any validation failure, ensuring that only cryptographically sound and protocol-compliant proofs are generated.

### Key Files (RISC Zero Implementation)

  * **`risc0-circuits/header-chain/guest/src/main.rs`**

      * This is the entry point for the **RISC Zero guest application**. It initializes the `Risc0Guest` environment and makes the crucial call to `circuits_lib::header_chain::header_chain_circuit` to execute the Bitcoin header chain verification logic inside the zkVM.

  * **`risc0-circuits/header-chain/guest/Cargo.toml`**

      * The package manifest for the guest-side code. It defines the `header-chain-guest` package and its dependencies, notably linking to the `circuits-lib` which contains the actual circuit logic.

  * **`risc0-circuits/header-chain/src/lib.rs`**

      * This file includes the `methods.rs` generated by the build script, which contains information about the guest methods (ELF image and method ID) that the host can use to prove the guest's execution. Because we use hard-coded method IDs and ELFs that are relocated by build.rs, we rely on those instead.

  * **`risc0-circuits/header-chain/build.rs`**

      * This is the **build script** for the host-side. It is responsible for:
          * Compiling the `header-chain-guest` code into a RISC Zero ELF binary.
          * Computing the unique **method ID** for the compiled guest program.
          * Handling environment variables (like `BITCOIN_NETWORK`) to configure the build.
          * Optionally using Docker for guest builds and copying the generated ELF binary to a designated `elfs` folder.

  * **`risc0-circuits/header-chain/Cargo.toml`**

      * The package manifest for the host-side crate. It defines the `header-chain` package and its build-time dependencies, including `risc0-build` for the RISC Zero toolchain integration.

---