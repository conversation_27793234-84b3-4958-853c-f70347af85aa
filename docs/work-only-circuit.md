# Work-Only Circuit Documentation

This document details the **Work-Only Circuit**, a specialized zero-knowledge virtual machine (zkVM) circuit designed to verify and commit the accumulated proof of work from a previously executed Bitcoin header chain circuit. Its primary purpose is to extract the `total_work` value and `genesis_state_hash` from a Header Chain Circuit proof and present them as a new, concise proof output.

### Core Logic

The core logic for the work-only circuit is contained within the [`mod.rs`](../circuits-lib/src/work_only/mod.rs) module. The main entry point, `work_only_circuit`, receives a `WorkOnlyCircuitInput` which includes the full output of a Header Chain Circuit execution.

The circuit begins by performing two crucial validation steps. First, it checks for method ID consistency, ensuring that the input proof's method ID matches a compile-time constant, `HEADER_CHAIN_METHOD_ID`, which is specific to the intended Bitcoin network. This prevents the circuit from verifying proofs from an incorrect or incompatible version of the Header Chain Circuit. Second, it uses the `env::verify()` function to cryptographically validate the entire `header_chain_circuit_output`. This step is a zero-knowledge check that proves the integrity and correctness of the preceding circuit's execution.

After successful verification, the circuit extracts the `total_work` and `genesis_state_hash` from the verified output. A private helper function, `work_conversion`, is then used to convert the 256-bit `total_work` value into a 128-bit representation, effectively truncating it to its lower 128 bits. The final output, a `WorkOnlyCircuitOutput`, contains this 128-bit work value and the genesis state hash, which are then committed to the zkVM's output.

## RISC Zero Implementation (`risc0-circuits/work-only`)

This section provides an overview of the `risc0-circuits/work-only` module, which defines the RISC Zero specific implementation and guest environment for the Work-Only Circuit.

### Key Files (RISC Zero Implementation)

  * **`risc0-circuits/work-only/guest/src/main.rs`**

      * This is the entry point for the **RISC Zero guest application**. It initializes the `Risc0Guest` environment and calls `circuits_lib::work_only::work_only_circuit` to execute the work verification logic inside the zkVM.

  * **`risc0-circuits/work-only/guest/Cargo.toml`**

      * The package manifest for the guest-side code. It defines the `work-only-guest` package and its dependencies, notably linking to `circuits-lib` which contains the core circuit logic.

  * **`risc0-circuits/work-only/src/lib.rs`**

      * This file includes the `methods.rs` generated by the build script, which contains information about the guest methods (ELF image and method ID) that the host can use to prove the guest's execution. Because we use hard-coded method IDs and ELFs that are relocated by build.rs, we rely on those instead.

  * **`risc0-circuits/work-only/build.rs`**

      * This is the **build script** for the host-side. It is responsible for:
          * Compiling the `work-only-guest` code into a RISC Zero ELF binary.
          * Computing the unique **method ID** for the compiled guest program.
          * Handling environment variables (like `BITCOIN_NETWORK` and `REPR_GUEST_BUILD`) to configure the build process, including optional Docker usage.
          * Copying the generated ELF binary to a designated `elfs` folder.

  * **`risc0-circuits/work-only/Cargo.toml`**

      * The package manifest for the host-side crate. It defines the `work-only` package and its build-time dependencies, including `risc0-build` for the RISC Zero toolchain integration.

-----