[public_keys]
sequencer_public_key = "036360e856310ce5d294e8be33fc807077dc56ac80d95d9cd4ddbd21325eff73f7"
sequencer_da_pub_key = "02588d202afcc1ee4ab5254c7847ec25b9a135bbda0f2bc69ee1a714749fd77dc9"
prover_da_pub_key = "03eedab888e45f3bdc3ec9918c491c11e5cf7af0a91f38b97fbc1e135ae4056601"

[da]
# fill here
node_url = "http://bitcoin_regtest:20443/wallet/admin"
# fill here
node_username = "admin"
# fill here
node_password = "admin"
tx_backup_dir = ""
da_private_key = "E9873D79C6D87DC0FB6A5778633389F4453213303DA61F20BD67FC233AA33262"

[storage]
# The path to the rollup's data directory. Paths that do not begin with `/` are interpreted as relative paths.
path = "resources/dbs/full-node-db"

[rpc]
# the host and port to bind the rpc server for
bind_host = "0.0.0.0"
bind_port = 12346
enable_subscriptions = true
max_subscriptions_per_connection = 100

[runner]
sequencer_client_url = "http://citrea_sequencer_regtest:12345"
include_tx_body = false
scan_l1_start_height = 1

# WARNING: State pruning is not completely implemented.
# Enabling this might lead to state corruption and therefore,
# avoid using it for now.
# [runner.pruning_config]
# distance = 6000
