private_key = "1212121212121212121212121212121212121212121212121212121212121212"
max_l2_blocks_per_commitment = 10
test_mode = false
deposit_mempool_fetch_limit = 10
block_production_interval_ms = 1000
da_update_interval_ms = 2000
bridge_initialize_params = "000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000008ac7230489e80000000000000000000000000000000000000000000000000000000000000000002d41203b48ffb437c2ee08ceb8b9bb9e5555c002fb304c112e7e1233fe233f2a3dfc1dac006306636974726561140000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016800000000000000000000000000000000000000000000000000000000000000"

[mempool_conf] # Mempool Configuration - https://github.com/ledgerwatch/erigon/wiki/Transaction-Pool-Design
pending_tx_limit = 100000
pending_tx_size = 200
queue_tx_limit = 100000
queue_tx_size = 200
base_fee_tx_limit = 100000
base_fee_tx_size = 200
max_account_slots = 16
