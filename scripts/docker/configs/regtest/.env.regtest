# SECRET_KEY="1111111111111111111111111111111111111111111111111111111111111111"

HOST=0.0.0.0
PORT=17000

READ_PARAMSET_FROM_ENV=1
READ_CONFIG_FROM_ENV=1

BITCOIN_RPC_URL=http://bitcoin_regtest:20443/wallet/admin
BITCOIN_RPC_USER=admin
BITCOIN_RPC_PASSWORD=admin

DB_HOST=postgres_db_regtest
DB_PORT=5432
DB_USER=clementine
DB_PASSWORD=clementine
# DB_NAME=clementine

CITREA_CHAIN_ID=62298
CITREA_RPC_URL=http://citrea_sequencer_regtest:12345
CITREA_LIGHT_CLIENT_PROVER_URL=http://citrea_light_client_prover_regtest:12349
BRIDGE_CONTRACT_ADDRESS=3100000000000000000000000000000000000002

SECURITY_COUNCIL=2:b496bfbae14987817c53d592be0aa66c45c7b94443c1f74551373f9ce34d2346,9c00b80d739933388f136f4519fed20cbaee4153899810703ca216d2320e20c4,994283e4c648fbeded4ecf579490622dd4469152e3b4bc8290607ed365fd29be
HEADER_CHAIN_PROOF_BATCH_SIZE=100

NETWORK=regtest
NUM_ROUND_TXS=3
NUM_KICKOFFS_PER_ROUND=100
NUM_SIGNED_KICKOFFS=2
BRIDGE_AMOUNT=1000000000
KICKOFF_AMOUNT=25000
OPERATOR_CHALLENGE_AMOUNT=130000000
COLLATERAL_FUNDING_AMOUNT=90000000
KICKOFF_BLOCKHASH_COMMIT_LENGTH=40
WATCHTOWER_CHALLENGE_BYTES=144
WINTERNITZ_LOG_D=4
WINTERNITZ_SECRET_KEY=2222222222222222222222222222222222222222222222222222222222222222
USER_TAKES_AFTER=200
OPERATOR_CHALLENGE_TIMEOUT_TIMELOCK=144
OPERATOR_CHALLENGE_NACK_TIMELOCK=432
DISPROVE_TIMEOUT_TIMELOCK=720
ASSERT_TIMEOUT_TIMELOCK=576
OPERATOR_REIMBURSE_TIMELOCK=12
WATCHTOWER_CHALLENGE_TIMEOUT_TIMELOCK=288
TIME_TO_SEND_WATCHTOWER_CHALLENGE=216
TIME_TO_DISPROVE=648
LATEST_BLOCKHASH_TIMEOUT_TIMELOCK=360
FINALITY_DEPTH=1
START_HEIGHT=2
GENESIS_HEIGHT=91000
GENESIS_CHAIN_STATE_HASH=b6e2031e9350450084537e72c05f81b497db4f41f63ee25b55e5de8924a2c5cf
OPERATOR_WITHDRAWAL_FEE_SATS=100000

BITVM_CACHE_PATH=/bitvm_cache.bin
JSON_LOGS=1
RUST_LOG=info
RUST_MIN_STACK=33554432
DBG_PACKAGE_HEX=1
RISC0_SKIP_BUILD=1
RISC0_DEV_MODE=1
BRIDGE_CIRCUIT_METHOD_ID_CONSTANT=e246ef42e7795aa55cf0f6677cbabb78dc1fc461c20c2addd8ff70c8c8d019db
CA_CERT_PATH=/certs/ca/ca.pem
SERVER_CERT_PATH=/certs/server/server.pem  
SERVER_KEY_PATH=/certs/server/server.key  
CLIENT_CERT_PATH=/certs/client/client.pem  
CLIENT_KEY_PATH=/certs/client/client.key  
AGGREGATOR_CERT_PATH=/certs/aggregator/aggregator.pem  
CLIENT_VERIFICATION=1
DISABLE_NOFN_CHECK=1
BRIDGE_NONSTANDARD=false
TELEMETRY_HOST=0.0.0.0
TELEMETRY_PORT=9000

VERIFIERS_PUBLIC_KEYS=034f355bdcb7cc0af728ef3cceb9615d90684bb5b2ca5f859ab0f0b704075871aa,02466d7fcae563e5cb09a0d1870bb580344804617879a14949cf22285f1bae3f27,023c72addb4fdf09af94f0c94d7fe92a386a7e70cf8a1d85916386bb2535c7b1b1,032c0b7cf95324a07d05398b240174dc0c2be444d96b159aa6c7f7b1e668680991
OPERATOR_XONLY_PKS=4f355bdcb7cc0af728ef3cceb9615d90684bb5b2ca5f859ab0f0b704075871aa,466d7fcae563e5cb09a0d1870bb580344804617879a14949cf22285f1bae3f27

VERIFIER_ENDPOINTS="https://regtest0_verifier.docker.internal:17000,https://regtest1_verifier.docker.internal:17000,https://regtest2_verifier.docker.internal:17000,https://regtest3_verifier.docker.internal:17000"
OPERATOR_ENDPOINTS="https://regtest0_operator.docker.internal:17000,https://regtest1_operator.docker.internal:17000"
