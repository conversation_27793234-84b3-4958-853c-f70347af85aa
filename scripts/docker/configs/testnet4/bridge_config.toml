# Host, port and index of the current actor (operator, verifier, or watchtower)
protocol_paramset = "regtest"

host = "0.0.0.0"
port = 17000
collateral_funding_amount = 99000000
timeout_block_count = 6
max_withdrawal_time_block_count = 4032

# Secret key of the current actor (operator or verifier)
secret_key = "1111111111111111111111111111111111111111111111111111111111111111"

# All of the verifiers public keys
num_verifiers = 4
verifiers_public_keys = [
    "034f355bdcb7cc0af728ef3cceb9615d90684bb5b2ca5f859ab0f0b704075871aa",
    "02466d7fcae563e5cb09a0d1870bb580344804617879a14949cf22285f1bae3f27",
    "023c72addb4fdf09af94f0c94d7fe92a386a7e70cf8a1d85916386bb2535c7b1b1",
    "032c0b7cf95324a07d05398b240174dc0c2be444d96b159aa6c7f7b1e668680991",
]

# All of the operators x-only public keys.
num_operators = 2
num_round_txs = 2
num_kickoffs_per_round = 2
operators_xonly_pks = [
    "4f355bdcb7cc0af728ef3cceb9615d90684bb5b2ca5f859ab0f0b704075871aa",
    "466d7fcae563e5cb09a0d1870bb580344804617879a14949cf22285f1bae3f27",
    "3c72addb4fdf09af94f0c94d7fe92a386a7e70cf8a1d85916386bb2535c7b1b1",
]

# Operator reimbursement addresses after the 2 week period.
operator_wallet_addresses = [
    "bcrt1pvaua4gvvglk27al5trh337xz8l8zzhgzageky0xt0dgv64xee8tqwwvzmf",
    "bcrt1pvaua4gvvglk27al5trh337xz8l8zzhgzageky0xt0dgv64xee8tqwwvzmf",
    "bcrt1pvaua4gvvglk27al5trh337xz8l8zzhgzageky0xt0dgv64xee8tqwwvzmf",
]
operator_withdrawal_fee_sats = 100000

operator_num_kickoff_utxos_per_tx = 10

# User can take funds back after this amount of blocks, if deposit fails.
user_takes_after = 200

# Bitcoin node configuration options
network = "regtest"
bitcoin_rpc_url = "http://bitcoin_testnet4:20443/wallet/admin"
bitcoin_rpc_user = "admin"
bitcoin_rpc_password = "admin"

# PostgreSQL database credentials.
db_host = "postgres_db"
db_port = 5432
db_user = "clementine"
db_password = "clementine"
db_name = "clementine"


confirmation_threshold = 1

citrea_rpc_url = "http://citrea_full_node:12346"
citrea_light_client_prover_url = "https://light-client-prover.testnet.citrea.xyz/"
citrea_chain_id = 5655
bridge_contract_address = "3100000000000000000000000000000000000002"

# Header chain prover's assumption to start with.
# header_chain_proof_path = "../core/src/test/data/first_1.bin"

# TLS certificate and key paths
server_cert_path = "/certs/server/server.pem"
server_key_path = "/certs/server/server.key"
ca_cert_path = "/certs/ca/ca.pem"
client_cert_path = "/certs/client/client.pem"
client_key_path = "/certs/client/client.key"
aggregator_cert_path = "/certs/aggregator/aggregator.pem"
client_verification = true
security_council = "1:50929b74c1a04954b78b4b6035e97a5e078a5a0f28ec96d547bfee9ace803ac0"

winternitz_secret_key = "2222222222222222222222222222222222222222222222222222222222222222"

socket_path = "/"

[telemetry]
host = "0.0.0.0"
port = 8081

[grpc]
max_message_size = 4194304
timeout_secs = 43200
tcp_keepalive_secs = 60
req_concurrency_limit = 300
ratelimit_req_count = 1000
ratelimit_req_interval_secs = 60
