{"abi": [{"type": "function", "name": "CODESEP_POS", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "EPOCH", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "INPUT_INDEX", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "KEY_VERSION", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "LIGHT_CLIENT", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract BitcoinLightClient"}], "stateMutability": "view"}, {"type": "function", "name": "SCHNORR_VERIFIER_PRECOMPILE", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "SIGHAS<PERSON>_ALL_HASH_TYPE", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "SIGHASH_SINGLE_ANYONECANPAY_HASH_TYPE", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "SPEND_TYPE_EXT", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "SPEND_TYPE_NO_EXT", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "SYSTEM_CALLER", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchWithdraw", "inputs": [{"name": "txIds", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "outputIds", "type": "bytes4[]", "internalType": "bytes4[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "moveTx", "type": "tuple", "internalType": "struct Bridge.Transaction", "components": [{"name": "version", "type": "bytes4", "internalType": "bytes4"}, {"name": "flag", "type": "bytes2", "internalType": "bytes2"}, {"name": "vin", "type": "bytes", "internalType": "bytes"}, {"name": "vout", "type": "bytes", "internalType": "bytes"}, {"name": "witness", "type": "bytes", "internalType": "bytes"}, {"name": "locktime", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "proof", "type": "tuple", "internalType": "struct Bridge.MerkleProof", "components": [{"name": "intermediateNodes", "type": "bytes", "internalType": "bytes"}, {"name": "blockHeight", "type": "uint256", "internalType": "uint256"}, {"name": "index", "type": "uint256", "internalType": "uint256"}]}, {"name": "shaScriptPubkeys", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "depositAmount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "depositPrefix", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "depositSuffix", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "depositTxIds", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "failedDepositVault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getAggregatedKey", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getWithdrawalCount", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_depositPrefix", "type": "bytes", "internalType": "bytes"}, {"name": "_depositSuffix", "type": "bytes", "internalType": "bytes"}, {"name": "_depositAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialized", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "processedTxIds", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "replaceDeposit", "inputs": [{"name": "replaceTx", "type": "tuple", "internalType": "struct Bridge.Transaction", "components": [{"name": "version", "type": "bytes4", "internalType": "bytes4"}, {"name": "flag", "type": "bytes2", "internalType": "bytes2"}, {"name": "vin", "type": "bytes", "internalType": "bytes"}, {"name": "vout", "type": "bytes", "internalType": "bytes"}, {"name": "witness", "type": "bytes", "internalType": "bytes"}, {"name": "locktime", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "proof", "type": "tuple", "internalType": "struct Bridge.MerkleProof", "components": [{"name": "intermediateNodes", "type": "bytes", "internalType": "bytes"}, {"name": "blockHeight", "type": "uint256", "internalType": "uint256"}, {"name": "index", "type": "uint256", "internalType": "uint256"}]}, {"name": "idToReplace", "type": "uint256", "internalType": "uint256"}, {"name": "shaScriptPubkeys", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "replacePrefix", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "replaceSuffix", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "safeWithdraw", "inputs": [{"name": "prepareTx", "type": "tuple", "internalType": "struct Bridge.Transaction", "components": [{"name": "version", "type": "bytes4", "internalType": "bytes4"}, {"name": "flag", "type": "bytes2", "internalType": "bytes2"}, {"name": "vin", "type": "bytes", "internalType": "bytes"}, {"name": "vout", "type": "bytes", "internalType": "bytes"}, {"name": "witness", "type": "bytes", "internalType": "bytes"}, {"name": "locktime", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "prepareProof", "type": "tuple", "internalType": "struct Bridge.MerkleProof", "components": [{"name": "intermediateNodes", "type": "bytes", "internalType": "bytes"}, {"name": "blockHeight", "type": "uint256", "internalType": "uint256"}, {"name": "index", "type": "uint256", "internalType": "uint256"}]}, {"name": "payoutTx", "type": "tuple", "internalType": "struct Bridge.Transaction", "components": [{"name": "version", "type": "bytes4", "internalType": "bytes4"}, {"name": "flag", "type": "bytes2", "internalType": "bytes2"}, {"name": "vin", "type": "bytes", "internalType": "bytes"}, {"name": "vout", "type": "bytes", "internalType": "bytes"}, {"name": "witness", "type": "bytes", "internalType": "bytes"}, {"name": "locktime", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "blockHeader", "type": "bytes", "internalType": "bytes"}, {"name": "script<PERSON>ub<PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setDepositScript", "inputs": [{"name": "_depositPrefix", "type": "bytes", "internalType": "bytes"}, {"name": "_depositSuffix", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFailedDepositVault", "inputs": [{"name": "_failedDepositVault", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setReplaceScript", "inputs": [{"name": "_replacePrefix", "type": "bytes", "internalType": "bytes"}, {"name": "_replaceSuffix", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "txId", "type": "bytes32", "internalType": "bytes32"}, {"name": "outputId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "withdrawalUTXOs", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "txId", "type": "bytes32", "internalType": "bytes32"}, {"name": "outputId", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "view"}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "wtxId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "txId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositReplaced", "inputs": [{"name": "index", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "oldTxId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "newTxId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "DepositScriptUpdate", "inputs": [{"name": "depositPrefix", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "depositSuffix", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "DepositTransferFailed", "inputs": [{"name": "wtxId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "txId", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FailedDepositVaultUpdated", "inputs": [{"name": "oldVault", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newVault", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "OperatorUpdated", "inputs": [{"name": "oldOperator", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newOperator", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ReplaceScriptUpdate", "inputs": [{"name": "replacePrefix", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "replaceSuffix", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "SafeWithdrawal", "inputs": [{"name": "payoutTx", "type": "tuple", "indexed": false, "internalType": "struct Bridge.Transaction", "components": [{"name": "version", "type": "bytes4", "internalType": "bytes4"}, {"name": "flag", "type": "bytes2", "internalType": "bytes2"}, {"name": "vin", "type": "bytes", "internalType": "bytes"}, {"name": "vout", "type": "bytes", "internalType": "bytes"}, {"name": "witness", "type": "bytes", "internalType": "bytes"}, {"name": "locktime", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "spentUtxo", "type": "tuple", "indexed": false, "internalType": "struct Bridge.UTXO", "components": [{"name": "txId", "type": "bytes32", "internalType": "bytes32"}, {"name": "outputId", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "index", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "utxo", "type": "tuple", "indexed": false, "internalType": "struct Bridge.UTXO", "components": [{"name": "txId", "type": "bytes32", "internalType": "bytes32"}, {"name": "outputId", "type": "bytes4", "internalType": "bytes4"}]}, {"name": "index", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "430:25565:10:-:0;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "430:25565:10:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;-1:-1:-1;430:25565:10;:::o;:::-;;:::i;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;1965:29;430:25565;1965:29;;;;;430:25565;;1965:29;-1:-1:-1;430:25565:10;;;;;;;;;;1965:29;-1:-1:-1;1965:29:10;;430:25565;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;:::o;:::-;;:::i;:::-;;;;;;;;:::i;:::-;:::o;:::-;-1:-1:-1;;;;;430:25565:10;;;;;;-1:-1:-1;;430:25565:10;;;;:::o;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;430:25565:10;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;16004:32;;;;;430:25565;;16066:77;16087:28;430:25565;16087:13;430:25565;16087:28;:::i;:::-;16074:9;:41;16066:77;:::i;:::-;16169:15;430:25565;16206:10;-1:-1:-1;16218:16:10;;;;;;430:25565;16236:3;16303:8;16425:44;16303:8;16087:13;16303:8;;;;:::i;:::-;430:25565;16274:92;16339:12;;;;;;:::i;:::-;;:::i;:::-;430:25565;;:::i;:::-;;;;-1:-1:-1;;;;;;430:25565:10;;16274:92;;430:25565;;16274:92;16380:26;;;:::i;:::-;16442:9;;;;:::i;:::-;16425:44;430:25565;;16453:15;;;;16425:44;;;:::i;:::-;;;;430:25565;16206:10;;430:25565;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;2962:10;:22;430:25565;;18856:95;20546:43;430:25565;18118:43;20546;430:25565;17861:59;430:25565;;17869:33;;17861:59;:::i;:::-;17930:63;430:25565;17938:13;430:25565;;:::i;:::-;17938:25;;17930:63;:::i;:::-;18118:43;;:::i;:::-;18358:13;;430:25565;18358:13;;18932:18;430:25565;18358:36;430:25565;18358:13;;;;:::i;:::-;430:25565;;;:::i;:::-;18358:36;:::i;:::-;18426:14;430:25565;;18426:14;;;18753:16;18426:50;430:25565;18426:14;;;;:::i;430:25565::-;18450:25;:14;;;;:::i;:::-;:25;;;:::i;:::-;18426:50;;:::i;:::-;18545:17;18510:56;430:25565;18545:17;430:25565;18545:17;;;;:::i;430:25565::-;18510:56;:::i;:::-;18714:17;;;;;:::i;:::-;18733:18;;;;;;;;:::i;:::-;18753:16;;:::i;:::-;18916:14;18901:13;18882:17;;;:::i;:::-;18901:13;;;:::i;:::-;18916:14;;;;:::i;:::-;18932:18;;;;:::i;:::-;430:25565;;;;:::i;:::-;;;;;:::i;:::-;18856:95;;:::i;:::-;18961:65;18969:24;18970:23;;;430:25565;;18970:14;430:25565;;;;;;;18970:23;430:25565;;;;;18970:23;18969:24;;430:25565;18969:24;18961:65;:::i;:::-;19036:30;:23;;430:25565;;18970:14;430:25565;;;;;;;19036:23;430:25565;;-1:-1:-1;;430:25565:10;18447:1;430:25565;;;;19036:30;20464:66;20435:19;19529:48;430:25565;19173:25;;;:::i;:::-;430:25565;;;;;;;;;19208:25;:35;:25;;;;:::i;:::-;:35;;:::i;:::-;19407:45;19425:1;2510:20:1;;;:::i;:::-;19415:11:10;;;19407:45;:::i;:::-;19529:48;:::i;:::-;20324:85;20332:43;20264:50;430:25565;17938:13;430:25565;;:::i;:::-;20139:85;20147:43;20103:26;430:25565;19831:13;430:25565;;:::i;:::-;;19986:77;430:25565;;20011:26;:14;;;;:::i;:::-;:26;:::i;:::-;19994:43;19986:77;:::i;:::-;20103:26;;:::i;:::-;430:25565;;:::i;:::-;20147:43;;:::i;:::-;20139:85;:::i;:::-;20277:25;430:25565;;;20277:25;:::i;:::-;20264:50;;:::i;:::-;430:25565;;:::i;20332:43::-;20324:85;:::i;:::-;20435:19;:::i;:::-;20472:21;;;20464:66;:::i;:::-;430:25565;;20546:43;;;;430:25565;;;;;;;;;;;;;;;;;;20546:43;;;;430:25565;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;21976:26;430:25565;;:::i;:::-;21976:26;:::i;430:25565::-;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;;;;2833:10;1102:42;2833:10;:27;430:25565;;;-1:-1:-1;430:25565:10;;;;4384:51;3793:19;4041:30;3793:19;3785:58;4384:51;3793:19;;;3785:58;:::i;:::-;3853:69;3861:26;;;3853:69;:::i;:::-;3933:18;3947:4;430:25565;;3728:11;430:25565;;;3728:11;430:25565;;3933:18;430:25565;;;;:::i;:::-;;;;;:::i;:::-;3947:4;430:25565;;4041:30;3728:11;430:25565;;-1:-1:-1;;;;;;430:25565:10;;;;;4231:72;430:25565;;-1:-1:-1;;;;;;430:25565:10;-1:-1:-1;;;;;430:25565:10;;;4327:42;430:25565;;4327:42;;;430:25565;1102:42;430:25565;;;;;;;;;;;4327:42;;;;430:25565;;4384:51;;;;;:::i;:::-;;;;430:25565;;;;;;-1:-1:-1;;;;;430:25565:10;;;;4450:90;;430:25565;;;;4450:90;430:25565;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;1728:28;430:25565;;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;430:25565:10;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;430:25565:10;2001:46;430:25565;;;;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;1930:29;430:25565;1930:29;;;;;;430:25565;;;;;;;;1930:29;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;1930:29;430:25565;;1930:29;;430:25565;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;17938:13;430:25565;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;17938:13:10;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;17938:13;430:25565;;;;;;;;;;-1:-1:-1;430:25565:10;19831:13;430:25565;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;19831:13:10;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;21976:13;430:25565;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;21976:13:10;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;430:25565:10;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;1865:26;430:25565;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1865:26;430:25565;;;;;;;-1:-1:-1;430:25565:10;;;;;;;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;1865:26;430:25565;;;;;;;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;2303:62:6;;:::i;:::-;-1:-1:-1;;;;;;;;;;;430:25565:10;;-1:-1:-1;;;;;;430:25565:10;;;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;;;;;;;-1:-1:-1;;;;;;;430:25565:10;3975:40:6;-1:-1:-1;;3975:40:6;430:25565:10;;;;;;;-1:-1:-1;;430:25565:10;;;;;16628:15;430:25565;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;966:10:8;-1:-1:-1;;;;;430:25565:10;;;2869:24:5;2865:96;;-1:-1:-1;;;;;;;;;;;430:25565:10;;-1:-1:-1;;;;;;430:25565:10;;;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;;966:10:8;430:25565:10;;;;;;;;-1:-1:-1;;;;;430:25565:10;3975:40:6;-1:-1:-1;;3975:40:6;430:25565:10;2865:96:5;2916:34;;;430:25565:10;2916:34:5;966:10:8;430:25565:10;;;;2916:34:5;430:25565:10;;-1:-1:-1;;430:25565:10;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;:::i;:::-;2303:62:6;;;;;:::i;:::-;5812:26:10;;430:25565;;-1:-1:-1;;;;;430:25565:10;;;;;;;5884:30;430:25565;;:::i;:::-;;:::i;:::-;5837:1;430:25565;;;;;;;;;5970:51;430:25565;;;;5970:51;430:25565;5837:1;430:25565;;;;;;;;;;;;;;;;;;;;;5884:30;430:25565;;;;;;:::i;:::-;;;5970:51;;;;;:::i;430:25565::-;;;;;;;;;;5884:30;430:25565;;-1:-1:-1;;430:25565:10;;;;5837:1;430:25565;;;;;;;;5970:51;430:25565;;5970:51;430:25565;;;;;;;;;;;;;;5884:30;430:25565;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;:::o;:::-;;;;;;;:::i;:::-;2303:62:6;;:::i;:::-;-1:-1:-1;;;;;430:25565:10;;;6267:33;;430:25565;;6349:18;430:25565;;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;6432:56;;430:25565;;;;;6432:56;430:25565;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;:::o;:::-;;;;;;:::i;:::-;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;-1:-1:-1;;430:25565:10;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;:::i;:::-;;:::o;:::-;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;1213:5;430:25565;;;;;;;;;-1:-1:-1;;430:25565:10;;;;1762:33;430:25565;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;;430:25565:10;;;;:::o;:::-;;;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;16904:36;430:25565;;;:::i;:::-;2303:62:6;;:::i;:::-;16869:20:10;430:25565;;-1:-1:-1;;;;;;430:25565:10;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;;;;16904:36;430:25565;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;1102:42;430:25565;;;;;;;;;-1:-1:-1;;430:25565:10;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;:::i;:::-;2303:62:6;;:::i;:::-;-1:-1:-1;;;;;;;;;;;430:25565:10;;-1:-1:-1;;;;;;430:25565:10;-1:-1:-1;;;;;430:25565:10;;;;;;;;;-1:-1:-1;;;;;;;;;;;430:25565:10;;;;2238:43:5;-1:-1:-1;;2238:43:5;430:25565:10;;;;;;;-1:-1:-1;;430:25565:10;;;;;;;1897:26;430:25565;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1897:26;430:25565;;;;;;;-1:-1:-1;430:25565:10;;;;;;;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2303:62:6;;;;;:::i;:::-;5066:69:10;5074:26;;;5066:69;:::i;:::-;-1:-1:-1;;;;;430:25565:10;;;;;;;5146:30;430:25565;;:::i;:::-;;:::i;:::-;5099:1;430:25565;;;;;;;;;5232:51;430:25565;;;;5232:51;430:25565;5099:1;430:25565;;;;;;;;;;;;;;;;;;;;5146:30;430:25565;;;;;;:::i;:::-;5146:30;430:25565;;-1:-1:-1;;430:25565:10;;;;5099:1;430:25565;;;;;;;;5232:51;430:25565;;5232:51;430:25565;;;;;;;;;;;;;;5146:30;430:25565;;;;;;;-1:-1:-1;;5146:30:10;430:25565;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;430:25565:10;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::i;:::-;;;3089:10;1102:42;3089:10;:27;:53;;;;430:25565;;;;3200:1;;;:::i;430:25565::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;3089:53;-1:-1:-1;;430:25565:10;3089:10;430:25565;;;;;-1:-1:-1;;;;;430:25565:10;3120:22;3089:53;;430:25565;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;:::i;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;:::i;:::-;16169:15;430:25565;-1:-1:-1;;;430:25565:10;;;;;;;;16169:15;430:25565;16169:15;430:25565;;;;;;16169:15;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;9456:2;430:25565;;;;;;;:::o;:::-;;4811:1:0;430:25565:10;;;;;;;:::o;:::-;22223:1:1;430:25565:10;;;22223:1:1;430:25565:10;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;430:25565:10;;;;;;;;:::o;:::-;-1:-1:-1;;430:25565:10;;;;;;;;:::o;:::-;;;;;;;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;21896:113;21976:26;430:25565;;:::i;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;5884:30;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;5884:30;430:25565;;;;;;;;;;5884:30;430:25565;;;;;;;;;;;:::o;:::-;-1:-1:-1;430:25565:10;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;;;;;;:::o;:::-;5146:30;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;430:25565:10;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;430:25565:10;;-1:-1:-1;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;430:25565:10;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;3961:30;430:25565;;:::i;:::-;3961:30;430:25565;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3961:30;430:25565;:::o;:::-;;;;-1:-1:-1;430:25565:10;;;;;3961:30;430:25565;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;3961:30;430:25565;:::o;:::-;;;-1:-1:-1;;3961:30:10;430:25565;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;5924:30;430:25565;;:::i;:::-;5924:30;430:25565;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5924:30;430:25565;:::o;:::-;5924:30;430:25565;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;5924:30;430:25565;:::o;:::-;;;-1:-1:-1;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;;-1:-1:-1;;430:25565:10;;;;:::o;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;10785:383::-;;10859:62;10880:13;430:25565;10867:9;:26;10859:62;:::i;:::-;430:25565;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;11121:40;430:25565;;;;;;;;;10950:72;;;430:25565;11048:15;430:25565;11080:26;;;:::i;:::-;11121:40;430:25565;;11145:15;;;;11121:40;;;:::i;:::-;;;;10785:383::o;430:25565::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;;430:25565:10;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;-1:-1:-1;;;;;;430:25565:10;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;-1:-1:-1;;430:25565:10;;;;;;;:::i;:::-;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;:::o;11779:3781::-;;;;12389:24;430:25565;11779:3781;12354:131;11779:3781;12241:95;12301:14;12078:13;;;12049:77;12057:35;430:25565;12078:13;;;;:::i;430:25565::-;12057:35;:::i;:::-;12049:77;:::i;:::-;12317:18;430:25565;12166:14;;;;;;12136:80;12144:37;430:25565;12166:14;;;;:::i;430:25565::-;12144:37;:::i;:::-;12136:80;:::i;:::-;12317:18;12286:13;12267:17;;;:::i;:::-;12286:13;;;:::i;:::-;12301:14;;;;;:::i;:::-;12317:18;;;;;:::i;12241:95::-;12389:24;;;12078:13;12389:24;;;430:25565;12434:30;;;;;:::i;:::-;12466:18;;;;430:25565;;12078:13;430:25565;;;;;;;;;12354:131;;;;;;:::i;:::-;;1004:42;-1:-1:-1;;;;;12354:131:10;;;;;;12346:171;12354:131;12267:17;12354:131;;;11779:3781;12346:171;;:::i;:::-;12078:13;12752:12;;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;12731:34;;;:::i;:::-;12723:83;;;:::i;:::-;12856:12;;;;:::i;:::-;430:25565;;;;;:::i;:::-;2510:20:1;;;:::i;:::-;12879:62:10;;-1:-1:-1;12895:1:10;12887:9;12879:62;:::i;:::-;12166:14;12981:13;;;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;12959:36;;;:::i;:::-;12951:86;;;:::i;:::-;13084:16;;;;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;13055:49;;;:::i;:::-;13047:102;;;:::i;:::-;13195:12;;;;:::i;:::-;430:25565;;;;;:::i;:::-;13195:35;;;:::i;:::-;13268:13;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;13291:13;;;;;:::i;:::-;:24;;;;:::i;:::-;13268:48;;;:::i;:::-;13390:16;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;13355:55;;;:::i;:::-;18729:17:1;;;12514:75:2;;;12417:178;;18729:17:1;13630::10;;;13622:48;;;:::i;:::-;12514:75:2;;;;-1:-1:-1;;;;;;430:25565:10;13898:14;;;;:::i;:::-;430:25565;;;;;:::i;:::-;;;;13898:55;;;:::i;:::-;430:25565;;;13993:2;13971:24;13963:64;;;:::i;:::-;14173:23;;;:::i;:::-;430:25565;;:::i;:::-;14160:46;;;:::i;:::-;14152:100;;;:::i;:::-;14283:23;;;:::i;:::-;430:25565;;:::i;:::-;14270:48;;;:::i;:::-;14262:94;;;:::i;:::-;14410:24;;;:::i;:::-;14397:52;;;;:::i;:::-;14389:99;;;:::i;:::-;14520:25;;;:::i;:::-;12514:75:2;;;;;-1:-1:-1;;;;;;430:25565:10;;12078:13;430:25565;14654:30;;12389:24;14654:30;;430:25565;;;:::i;:::-;14654:30;430:25565;;14654:30;;;;;;;;:::i;:::-;12078:13;430:25565;;;;;;:::i;:::-;14647:38;;;;12267:17;14647:38;14304:1;12389:24;14647:38;;;;;15542:10;14647:38;15452:59;14647:38;15163:54;15089:39;15014:33;14647:38;14801:181;14647:38;14801:181;14647:38;15236:79;14647:38;15244:49;14647:38;12267:17;14647:38;430:25565;;;:::i;:::-;;;;:::i;:::-;14864:16;14882:17;12317:18;14864:16;;;:::i;:::-;14882:17;;;:::i;:::-;430:25565;;:::i;:::-;;12078:13;430:25565;14801:181;;;12389:24;14801:181;;;:::i;:::-;;430:25565;;14801:181;;;;;;:::i;:::-;430:25565;;:::i;:::-;15014:33;:::i;:::-;15089:39;;:::i;:::-;15189:27;430:25565;;15189:27;:::i;15163:54::-;15244:49;;:::i;:::-;15236:79;:::i;:::-;430:25565;;:::i;:::-;;;;-1:-1:-1;;;;;;430:25565:10;;12389:24;15358:79;;430:25565;;15452:59;15488:15;430:25565;12078:13;430:25565;15452:59;;;;;:::i;:::-;;;;15542:10;:::i;14647:38::-;;:::i;12354:131::-;;;;12389:24;12354:131;12389:24;12354:131;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;430:25565;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;8667:12;430:25565;-1:-1:-1;;;430:25565:10;;;;;;;;8667:12;430:25565;8667:12;430:25565;;;;;;8667:12;-1:-1:-1;430:25565:10;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;430:25565:10;;;;:::o;:::-;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;7079:3368;8985:48;8475:83;9858:31;7079:3368;7763:44;7713:40;7779:1;7079:3368;;7713:40;;:::i;:::-;7771:9;;;7763:44;:::i;:::-;8004:10;;;8542:15;430:25565;8004:33;430:25565;8004:10;;;;:::i;8475:83::-;8577:20;8568:52;8576:21;8577:20;;;430:25565;;18970:14;430:25565;;;;;;;8576:21;8568:52;:::i;:::-;8630:27;:20;;430:25565;;18970:14;430:25565;;;;;;;8630:27;8667:23;;;:::i;8985:48::-;9750:77;9758:43;9690:50;430:25565;8880:1;430:25565;;:::i;:::-;9572:78;9580:43;9536:26;430:25565;9264:13;430:25565;;:::i;:::-;;9419:77;430:25565;;9444:26;:14;;;;:::i;9536:26::-;430:25565;;:::i;9580:43::-;9572:78;:::i;9690:50::-;430:25565;;:::i;9758:43::-;9750:77;:::i;:::-;9858:31;:::i;:::-;430:25565;8035:1;430:25565;;;7779:1;430:25565;9919:40;;;;;:::i;:::-;-1:-1:-1;9972:8:10;;10085:87;;430:25565;10148:23;8667:12;430:25565;10148:23;:::i;:::-;8004:10;430:25565;;;;;;;;;;;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;;10131:15;430:25565;;;;;;;;;;;;;;;;;;;10085:87;;;;10263:58;8035:1;430:25565;;;;10200:18;430:25565;;;;;;;;;;7779:1;430:25565;10200:49;;;;;:::i;:::-;;10263:58;:::i;9969:472::-;10357:73;;430:25565;10406:23;8667:12;430:25565;10406:23;:::i;430:25565::-;;;;;:::i;:::-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;20602:819::-;;;20786:11;;;:::i;:::-;20799:8;;;;;;:::i;:::-;20809:7;;;;;;;;:::i;:::-;20818:8;;;;;;;;;;;:::i;:::-;20828:11;;;;;;;;;:::i;:::-;20841:12;;;;;;;;:::i;:::-;20758:96;;;;:::i;:::-;20893:7;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;20872:29;;;:::i;:::-;20864:71;;;:::i;:::-;20975:8;;;;:::i;:::-;430:25565;;;;;:::i;:::-;20953:31;;;:::i;:::-;20945:74;;;:::i;:::-;21078:7;;;;:::i;:::-;430:25565;;;;;:::i;:::-;2510:20:1;;;:::i;:::-;21184:11:10;;;;;;;:::i;:::-;430:25565;;;;;:::i;:::-;21155:47;;;;:::i;:::-;21147:93;;;:::i;:::-;20799:8;21288:17;;430:25565;21314:23;;;;;:::i;:::-;20809:7;430:25565;;-1:-1:-1;;;21259:92:10;;430:25565;;;21259:92;;21339:11;;430:25565;;21339:11;;21259:92;;;;;;:::i;:::-;-1:-1:-1;;;;;1004:42:10;;;21259:92;1004:42;21259:92;;20799:8;21259:92;;;;;;21251:132;21259:92;-1:-1:-1;21259:92:10;;;21251:132;;:::i;:::-;21394:20;20602:819;:::o;654:66:1:-;;;;:::o;:::-;430:25565:10;;-1:-1:-1;;;654:66:1;;;;;;;;;;;430:25565:10;654:66:1;430:25565:10;;;654:66:1;-1:-1:-1;;;654:66:1;;;;;;;;;;;:::o;:::-;430:25565:10;;-1:-1:-1;;;654:66:1;;;;;;;;;;;430:25565:10;-1:-1:-1;;;430:25565:10;;;654:66:1;;;;11841:818;12147:43;2510:20;;;:::i;:::-;430:25565:10;;;12061:76:1;-1:-1:-1;;12069:29:1;;;12061:76;:::i;:::-;12155:14;;12147:43;:::i;:::-;12245:1;430:25565:10;;12245:1:1;430:25565:10;;;12508:37:1;;;;:::i;:::-;430:25565:10;-1:-1:-1;;12563:19:1;;654:66;;12627:25;;;:::i;654:66::-;430:25565:10;;-1:-1:-1;;;654:66:1;;;;;;;;;;;430:25565:10;654:66:1;430:25565:10;;;654:66:1;;;;430:25565:10;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;10344:924:2;;;;10463:12;;10459:55;;430:25565:10;18447:1;430:25565;;18447:1;430:25565;;;10569:13:2;18447:1:10;10561:70:2;10569:13;;:38;;;;10344:924;10561:70;;:::i;:::-;10642:620;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;;;;;10569:38;430:25565:10;;;;10586:21:2;;10569:38;;;10459:55;430:25565:10;;;;;;;;;:::i;:::-;10474:1:2;430:25565:10;;10491:12:2;:::o;10344:924::-;;;;10463:12;;10459:55;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;;;;;;10344:924;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;430:25565:10;10642:620:2;;;;21999:2:10;10642:620:2;;;;;;430:25565:10;10642:620:2;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;21999:2:10;10642:620:2;;;10344:924;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;;;;;;12895:1:10;10642:620:2;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;;;;;10344:924;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;;;;;;14304:1:10;10642:620:2;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;;;;;10344:924;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;;;;;;14431:2:10;10642:620:2;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;;;;;10344:924;;10561:70;430:25565:10;;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;430:25565:10;10642:620:2;;;;12389:24:10;10642:620:2;;;;;;430:25565:10;10642:620:2;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;-1:-1:-1;;10642:620:2;;12389:24:10;10642:620:2;;;10344:924;;10561:70;25486:2:10;430:25565;;10586:21:2;;10561:70;:::i;:::-;25486:2:10;10642:620:2;;;;;25486:2:10;10642:620:2;25486:2:10;10642:620:2;;;;;430:25565:10;10642:620:2;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;;;;;;10344:924;;10561:70;18417:2:1;430:25565:10;;10586:21:2;;10561:70;:::i;:::-;10642:620;;;;;;;;18417:2:1;10642:620:2;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;;;;;;10344:924;;;;10463:12;;10459:55;;430:25565:10;;;;;;;;10569:13:2;;10561:70;10569:13;;:38;;;;10561:70;;:::i;:::-;10642:620;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10344:924;;;:::o;10642:620::-;;;;;;;;;;;10569:38;430:25565:10;;;;10586:21:2;;10569:38;;;10459:55;430:25565:10;;;;;;;;;;:::i;3420:579:0:-;3831:43;2976:8:10;3831:43:0;;:::i;:::-;-1:-1:-1;;3892:28:0;;430:25565:10;;2976:8;3963:29:0;;;:::i;430:25565:10:-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;23959:13;430:25565;;;;;;;:::o;:::-;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;430:25565:10;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;23644:1542::-;;;;;;;23836:31;;430:25565;18401:19:1;;;:::i;:::-;430:25565:10;;;;;;;:::i;:::-;23836:31;;;;;;;;;;430:25565;23836:31;;430:25565;23905:80;;23922:62;23929:54;23952:30;23959:22;:13;430:25565;23974:6;430:25565;;;;23959:22;-1:-1:-1;;;;;430:25565:10;;;23952:30;5929:18:1;430:25565:10;;;;;;;;;;5965:22:1;;430:25565:10;;;;5803:18:1;5792:29;;5791:77;430:25565:10;;;5964:30:1;5965:22;;;;430:25565:10;;;5917:30:1;;5916:79;430:25565:10;;;;;;6056:7:1;430:25565:10;;;6043:21:1;5671:400;;23929:54:10;430:25565;;-1:-1:-1;;;;;;430:25565:10;;;23922:62;430:25565;;23905:80;;;;;;430:25565;;-1:-1:-1;;;;;430:25565:10;;;;;;;;;23898:88;;23836:31;23898:88;;;;;23836:31;;430:25565;24046:50;;17514:17:1;23898:88:10;;17514:17:1;12514:75:2;430:25565:10;;;12514:75:2;;;430:25565:10;13081:136:2;;17514:17:1;430:25565:10;;24046:50;;;;;;430:25565;;;;;;;;;;;;24039:58;;23836:31;24039:58;;;;;23836:31;;430:25565;;24135:24;24039:58;;430:25565;;;24135:24;;;;;;430:25565;;:::i;:::-;24128:32;;23836:31;24128:32;;;;;23836:31;24128:32;24192:34;;;;:::i;:::-;-1:-1:-1;;;24264:34:10;;;:::i;:::-;24445:15;;;:::i;:::-;430:25565;-1:-1:-1;;;;;;430:25565:10;;;;;;24522:37;;24445:22;23836:31;24522:37;;;:::i;:::-;;430:25565;;24522:37;;;;;;;;:::i;:::-;430:25565;;:::i;:::-;24499:62;;;;:::i;:::-;430:25565;;;:::i;:::-;;;;:::i;:::-;;;;:::i;:::-;;;;:::i;:::-;;;;:::i;:::-;;;;:::i;:::-;;;;24594:202;;;23836:31;24594:202;;;;;;:::i;:::-;;430:25565;;24594:202;;;;;;;;:::i;:::-;430:25565;;:::i;:::-;24828:33;;;;:::i;:::-;24903:34;;;;:::i;:::-;430:25565;;24998:27;;;:::i;:::-;24972:54;;;:::i;:::-;25065:18;;:::i;:::-;25101:56;;;;:::i;:::-;25093:86;;;:::i;2637:355:4:-;430:25565:10;2637:355:4;2921:64;2637:355;430:25565:10;2921:50:4;2637:355;;430:25565:10;;;;;;;;;;2921:50:4;;;430:25565:10;;;;;;:::i;:::-;;;;;;;;2921:50:4;;;;;;;;;;:::i;:::-;:64;:::i;430:25565:10:-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;4360:974:0;;4707:45;2510:20:1;;;:::i;:::-;430:25565:10;;;4612:85:0;-1:-1:-1;;4620:38:0;;;4612:85;:::i;:::-;18447:1:10;4715:16:0;4707:45;:::i;:::-;18447:1:10;430:25565;;18447:1;430:25565;;;4845:13:0;-1:-1:-1;4840:263:0;4860:10;18447:1:10;4860:10:0;;;;5273:54;5142:41;;;5297:29;:25;5142:41;;;;:::i;:::-;5193:63;-1:-1:-1;;5201:32:0;;;5193:63;:::i;5297:25::-;:29;:::i;:::-;5273:54;;:::i;4872:3::-;4920:41;;;;:::i;:::-;430:25565:10;;4975:63:0;-1:-1:-1;;4983:32:0;;;4975:63;:::i;:::-;430:25565:10;18447:1;430:25565;;;18447:1;430:25565;;;18447:1;430:25565;;;;;;;;18447:1;5052:40:0;;;;:::i;:::-;4872:3;430:25565:10;4845:13:0;;;4360:974;4707:45;2510:20:1;;;:::i;4707:45:0:-;4811:1;430:25565:10;;;4811:1:0;430:25565:10;;;5142:41:0;;;;:::i;:::-;430:25565:10;;5193:63:0;-1:-1:-1;;5201:32:0;;;5193:63;:::i;:::-;430:25565:10;;;;;;;5297:29:0;5273:54;5297:29;;:::i;4360:974::-;;4707:45;2510:20:1;;;:::i;:::-;430:25565:10;;;4612:85:0;-1:-1:-1;;4620:38:0;;;4612:85;:::i;:::-;23836:31:10;4715:16:0;4707:45;:::i;:::-;4811:1;430:25565:10;;4811:1:0;430:25565:10;;;4845:13:0;-1:-1:-1;4840:263:0;4860:10;23836:31:10;4860:10:0;;;;5273:54;5142:41;;;5297:29;:25;5142:41;;;;:::i;4872:3::-;4920:41;;;;:::i;:::-;430:25565:10;;4975:63:0;-1:-1:-1;;4983:32:0;;;4975:63;:::i;:::-;430:25565:10;4811:1:0;430:25565:10;;;4811:1:0;430:25565:10;;;4811:1:0;430:25565:10;;;;;;;;4811:1:0;5052:40;;;;:::i;:::-;4872:3;430:25565:10;4845:13:0;;;22269:774:10;;430:25565;;;;;22405:15;;22401:58;;22486:2;22553:13;;;;;;;22901:11;;;:::i;:::-;22914:7;;;;;23025:11;;;;430:25565;22269:774;:::o;22923:3::-;22946:4;;;;;:::i;:::-;430:25565;-1:-1:-1;;;;;;430:25565:10;;;22946:4;:12;22954:4;;;;;:::i;:::-;-1:-1:-1;;;;;;430:25565:10;;;22946:12;-1:-1:-1;;;;;;430:25565:10;;;22946:12;22942:63;;430:25565;;22892:20;;22942:63;22978:12;;;;-1:-1:-1;22978:12:10;:::o;22546:289::-;22582:163;;;;;;;;22486:2;22582:163;;;;22758:67;22546:289;22758:67;22798:12;;;;-1:-1:-1;22798:12:10;:::o;22401:58::-;22436:12;;;-1:-1:-1;22436:12:10;:::o;21674:216::-;430:25565;21776:13;430:25565;;:::i;:::-;21858:2;430:25565;;;;;;;10569:13:2;;10561:70;10569:13;;:38;;;;10561:70;;:::i;:::-;10642:620;;;430:25565:10;10642:620:2;;;;21858:2:10;10642:620:2;;;;;;;21858:2:10;10642:620:2;;;;;;;;;;;21821:41:10;;;;;;:::i;10642:620:2:-;;;;;;;21858:2:10;10642:620:2;;;10569:38;430:25565:10;;;;10586:21:2;;10569:38;;;2658:162:6;-1:-1:-1;;;;;;;;;;;430:25565:10;-1:-1:-1;;;;;430:25565:10;966:10:8;2717:23:6;2713:101;;2658:162::o;27793:991:1:-;2510:20;;;:::i;:::-;28050:10;;;;:43;;;;27793:991;28046:86;;28160:1;430:25565:10;;28160:1:1;430:25565:10;;;28194:13:1;430:25565:10;28189:492:1;28209:9;;;;;;430:25565:10;;;28755:22:1;27793:991;:::o;28220:3::-;430:25565:10;;;;28301:22:1;;;28297:73;;28464:37;;;;:::i;:::-;-1:-1:-1;;28519:23:1;;28515:74;;430:25565:10;;;;;;;28651:19:1;28160:1;430:25565:10;;28194:13:1;;28515:74;28562:12;;;;;430:25565:10;28562:12:1;:::o;28050:43::-;-1:-1:-1;;;28064:29:1;;28050:43;;29056:1004;2510:20;;;:::i;:::-;29319:11;;;;:44;;;;29056:1004;29315:87;;29430:1;430:25565:10;;29430:1:1;430:25565:10;;;29464:13:1;430:25565:10;29459:497:1;29479:10;;;;;;430:25565:10;;;30030:23:1;29056:1004;:::o;29491:3::-;430:25565:10;;;;29572:23:1;;;29568:74;;29785:39;;;;:::i;:::-;-1:-1:-1;;29842:23:1;;29838:74;;430:25565:10;;;;;;;29926:19:1;29430:1;430:25565:10;;29464:13:1;;29319:44;-1:-1:-1;;;29334:29:1;;29319:44;;1060:871:0;-1:-1:-1;1317:13:0;-1:-1:-1;1332:10:0;12895:1:10;1332:10:0;;;;430:25565:10;;1898:26:0;1060:871;:::o;1344:3::-;430:25565:10;;;1425:26:0;;;1421:77;;1592:43;;;;:::i;:::-;-1:-1:-1;;1653:32:0;;1649:83;;430:25565:10;;;;;;;1794:19:0;12895:1:10;430:25565;1317:13:0;;1060:871;1222:11;;1218:54;;430:25565:10;1317:13:0;430:25565:10;1312:512:0;1332:10;;;;;;430:25565:10;;;1898:26:0;1060:871;:::o;1344:3::-;430:25565:10;;;;1425:26:0;;;1421:77;;1592:43;;;;:::i;:::-;-1:-1:-1;;1653:32:0;;1649:83;;430:25565:10;;;;;;;1794:19:0;430:25565:10;;;1317:13:0;;1218:54;1249:12;;430:25565:10;1249:12:0;:::o;430:25565:10:-;;;;:::o;:::-;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;21812:827:1;;;;2510:20;;;:::i;:::-;22037:76;-1:-1:-1;;22045:29:1;;;22037:76;:::i;:::-;22131:15;;430:25565:10;;;22223:18:1;;;:::i;:::-;-1:-1:-1;22252:213:1;22273:11;;;;;;22482:39;22606:26;22482:39;;;;;;;:::i;:::-;430:25565:10;22531:58:1;-1:-1:-1;;22539:19:1;;;22531:58;:::i;:::-;22606:26;:::i;22286:5::-;22314:39;22439:15;22314:39;;22223:1;22314:39;;;:::i;:::-;430:25565:10;22367:58:1;-1:-1:-1;;22375:19:1;;;22367:58;:::i;22439:15::-;22286:5;430:25565:10;22257:14:1;;;;430:25565:10;;;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;25779:214;25900:18;430:25565;25900:18;25779:214;430:25565;;;;;;;:::i;:::-;25900:18;;;;;;;;;430:25565;25900:18;;25942:43;25900:18;;430:25565;;;25942:43;;;;;;;430:25565;;;;;;;;;;:::i;:::-;25935:51;;25900:18;25935:51;;;;;25900:18;25935:51;25779:214;:::o;430:25565::-;;;;;;;;;:::i;25318:455::-;;;;430:25565;;25486:2;25466:22;;:48;;;;;25318:455;430:25565;;;;25581:1;25565:22;25671:48;25565:22;;;;:::i;:::-;430:25565;25671:48;25486:2;430:25565;25671:48;;;;;;;;;:::i;:::-;25623:97;;1213:5;25623:97;;;25740:26;25623:97;;:::i;:::-;25671:48;430:25565;;;25740:26;;;;;;:::i;430:25565::-;25486:2;430:25565;-1:-1:-1;;;430:25565:10;;;;;;;;;;;;;;;;;;;;25466:48;25512:2;25492:22;;;25466:48;;;21427:241;430:25565;21541:13;430:25565;;:::i;:::-;21617:2;430:25565;;;;;;;10569:13:2;;10561:70;10569:13;;:38;;;;10561:70;;:::i;:::-;10642:620;;;;;;;;21617:2:10;10642:620:2;;;;;;;;;;;;;;;;;;;21587:34:10;;;10642:620:2;430:25565:10;;;;;;;;;;;21617:2;430:25565;;;;10642:620:2;430:25565:10;;;;21427:241;:::o;430:25565::-;-1:-1:-1;;21617:2:10;430:25565;;;;21541:13;430:25565;;;;;;;-1:-1:-1;430:25565:10;;;;10642:620:2;;;;;;;;;;;431:320:0;;;;;;;;;;;;;430:25565:10;;;;;;;;;669:61:0;;;430:25565:10;;;;;;;;;;;;;;;;;;;-1:-1:-1;430:25565:10;;;;;;;-1:-1:-1;430:25565:10;;;;;;;;;;;;669:61:0;2921:50:4;;669:61:0;;;;430:25565:10;669:61:0;;;;;:::i;:::-;:75;;;:::i;14980:394:1:-;;15185:34;14980:394;15185:34;:::i;:::-;-1:-1:-1;;15233:29:1;;15229:78;;430:25565:10;15324:6:1;430:25565:10;;;15324:6:1;430:25565:10;;;;15324:6:1;430:25565:10;;;;;;;;;;;;;;14980:394:1;:::o;15229:78::-;430:25565:10;;;;15278:18:1;:::o;2254:783:0:-;;2470:37;;;;:::i;:::-;430:25565:10;;-1:-1:-1;;2521:38:0;;2517:96;;2667:1;430:25565:10;;2667:1:0;430:25565:10;;;2649:36:0;;;-1:-1:-1;2696:310:0;2716:16;;;;;;3016:14;;;;2254:783;:::o;2734:3::-;430:25565:10;;;;;;;;;;;;2782:47:0;;;;:::i;:::-;430:25565:10;;-1:-1:-1;;2847:32:0;;2843:98;;430:25565:10;2667:1:0;430:25565:10;;;2667:1:0;430:25565:10;;;2667:1:0;430:25565:10;;;;;;;;2667:1:0;2955:40;;;;:::i;:::-;2734:3;2701:13;430:25565:10;;2701:13:0;;;2843:98;430:25565:10;;;;;;;;;2899:27:0;:::o;2517:96::-;430:25565:10;;;;;;2575:27:0;:::o;9609:335:1:-;9757:181;;9609:335;9757:181;;;;;;;;;;;;;;;;;;;9609:335;:::o;430:25565:10:-;;;;;;;;;;;:::o;2999:704:1:-;;3120:36;2976:8:10;3120:36:1;;:::i;:::-;430:25565:10;;;;3171:13:1;;;3167:70;;3262:18;2976:8:10;3262:18:1;:12;430:25565:10;;3262:12:1;;:::i;:::-;430:25565:10;;;;3262:18:1;-1:-1:-1;3246:84:1;;2976:8:10;3380:1:1;3368:13;;3380:1;;3438:7;3407:41;3421:26;3428:18;3397:51;3438:7;;2976:8:10;3438:7:1;:::i;:::-;12514:75:2;;;;-1:-1:-1;;;;;;430:25565:10;;13493:136:2;3428:18:1;430:25565:10;;;;3421:26:1;430:25565:10;;;7048:1:1;430:25565:10;;;7048:1:1;430:25565:10;;7041:21:1;6959:110;;3407:41;430:25565:10;;;;3364:297:1;3481:1;3469:13;;3481:1;;3539:7;3508:41;3522:26;3529:18;3498:51;3539:7;;2976:8:10;3539:7:1;:::i;:::-;12514:75:2;;;;-1:-1:-1;;;;;;430:25565:10;;13081:136:2;3529:18:1;430:25565:10;;;;3522:26:1;430:25565:10;;;;6370:10:1;6359:21;430:25565:10;;;;;6358:61:1;430:25565:10;;;;;;;;;;;6467:21:1;;6238:257;3508:41;430:25565:10;;;;3465:196:1;3570:13;;3582:1;3570:13;;3566:95;;3465:196;21394:20:10;20602:819;:::o;3566:95:1:-;3599:51;3640:7;;;3623:26;3630:18;3609:41;3640:7;;2976:8:10;3640:7:1;:::i;:::-;12514:75:2;;;;-1:-1:-1;;;;;;430:25565:10;;12875:136:2;3630:18:1;430:25565:10;;;;3246:84:1;-1:-1:-1;;;430:25565:10;-1:-1:-1;430:25565:10;;3296:23:1:o;3167:70::-;3217:7;3211:14;3217:7;;;;2976:8:10;3217:7:1;;;:::i;:::-;430:25565:10;;;;3211:14:1;3200:26;430:25565:10;2976:8;430:25565;;3200:26:1;:::o;2999:704::-;;;;3120:36;;;;:::i;:::-;430:25565:10;;;;3171:13:1;;;3167:70;;3262:18;430:25565:10;3262:18:1;:12;430:25565:10;;3262:12:1;;:::i;:18::-;-1:-1:-1;3246:84:1;;430:25565:10;3380:1:1;3368:13;;3380:1;;3438:7;3421:26;3428:18;3397:51;3438:7;;3407:41;3438:7;;:::i;3364:297::-;3481:1;3469:13;;3481:1;;3539:7;3522:26;3529:18;3498:51;3539:7;;3508:41;3539:7;;:::i;3465:196::-;3570:13;;;3582:1;3570:13;;3566:95;;3465:196;;;21394:20:10;20602:819;:::o;3566:95:1:-;3599:51;3640:7;;;3630:18;3609:41;3640:7;;3623:26;3640:7;;:::i;3599:51::-;3566:95;;;;;3246:84;-1:-1:-1;;;430:25565:10;-1:-1:-1;430:25565:10;;;-1:-1:-1;3296:23:1:o;3167:70::-;3217:7;3211:14;3217:7;;;;;;;;:::i;23049:452:10:-;430:25565;;23148:19;;;23144:60;;23272:2;23262:12;;430:25565;;23272:2;23345:150;;;23272:2;23345:150;;;;23049:452;:::o;430:25565::-;;;-1:-1:-1;;;430:25565:10;;23272:2;430:25565;;;;;;;;;;;;;;-1:-1:-1;;;430:25565:10;;;;;;;20911:536:1;;430:25565:10;;;21042:1:1;430:25565:10;;;21042:1:1;430:25565:10;;;21025:24:1;21021:73;;21235:1;430:25565:10;;21235:1:1;430:25565:10;;;21212:31:1;;;:::i;:::-;430:25565:10;-1:-1:-1;;21258:29:1;;21254:78;;430:25565:10;21042:1:1;430:25565:10;;;21042:1:1;430:25565:10;;;21042:1:1;430:25565:10;;;;;;;;20911:536:1;:::o;13785:388::-;;430:25565:10;;;13921:2:1;430:25565:10;;;13921:2:1;430:25565:10;;;13905:24:1;13901:78;;14114:2;430:25565:10;;;;;;;14086:31:1;;;:::i;13901:78::-;-1:-1:-1;;;430:25565:10;;;-1:-1:-1;13945:23:1:o;1550:446::-;;430:25565:10;1667:7:1;;;;:::i;:::-;430:25565:10;;;1661:22:1;1657:93;;1781:4;430:25565:10;1769:7:1;;;;:::i;:::-;430:25565:10;;;;;;;;;1763:22:1;1759:93;;1871:7;430:25565:10;1871:7:1;1883:4;1871:7;;:::i;:::-;430:25565:10;;;;;;;;;1865:22:1;1861:93;;430:25565:10;1550:446:1;:::o;1861:93::-;1910:1;1903:8;:::o;1759:93::-;1801:8;;1808:1;1801:8;:::o;1657:93::-;1699:8;;1706:1;1699:8;:::o", "linkReferences": {}}, "methodIdentifiers": {"CODESEP_POS()": "b2497e70", "EPOCH()": "a0dc2758", "INPUT_INDEX()": "428bcd35", "KEY_VERSION()": "1369ac3e", "LIGHT_CLIENT()": "e613ae00", "SCHNORR_VERIFIER_PRECOMPILE()": "9a4f308d", "SIGHASH_ALL_HASH_TYPE()": "0bd89ab7", "SIGHASH_SINGLE_ANYONECANPAY_HASH_TYPE()": "6cf7d641", "SPEND_TYPE_EXT()": "23dacd29", "SPEND_TYPE_NO_EXT()": "092ac5d4", "SYSTEM_CALLER()": "d761753e", "acceptOwnership()": "79ba5097", "batchWithdraw(bytes32[],bytes4[])": "19854623", "deposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),bytes32)": "fb11d7b9", "depositAmount()": "419759f5", "depositPrefix()": "5b4f894d", "depositSuffix()": "8752b6b2", "depositTxIds(uint256)": "06592167", "failedDepositVault()": "a670e7ed", "getAggregatedKey()": "3c918b6c", "getWithdrawalCount()": "781952a8", "initialize(bytes,bytes,uint256)": "41260137", "initialized()": "158ef93e", "operator()": "570ca735", "owner()": "8da5cb5b", "pendingOwner()": "e30c3978", "processedTxIds(bytes32)": "4379caa5", "renounceOwnership()": "715018a6", "replaceDeposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),uint256,bytes32)": "2594f107", "replacePrefix()": "6b0b5a94", "replaceSuffix()": "f42cb4fc", "safeWithdraw((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),(bytes4,bytes2,bytes,bytes,bytes,bytes4),bytes,bytes)": "9072f747", "setDepositScript(bytes,bytes)": "f8e655d2", "setFailedDepositVault(address)": "85fb7151", "setOperator(address)": "b3ab15fb", "setReplaceScript(bytes,bytes)": "7ec9732a", "transferOwnership(address)": "f2fde38b", "withdraw(bytes32,bytes4)": "8786dba7", "withdrawalUTXOs(uint256)": "471ba1e3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"wtxId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositId\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"oldTxId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"newTxId\",\"type\":\"bytes32\"}],\"name\":\"DepositReplaced\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"depositPrefix\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"depositSuffix\",\"type\":\"bytes\"}],\"name\":\"DepositScriptUpdate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"wtxId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositId\",\"type\":\"uint256\"}],\"name\":\"DepositTransferFailed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldVault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newVault\",\"type\":\"address\"}],\"name\":\"FailedDepositVaultUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldOperator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"OperatorUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferStarted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"replacePrefix\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"replaceSuffix\",\"type\":\"bytes\"}],\"name\":\"ReplaceScriptUpdate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes4\",\"name\":\"version\",\"type\":\"bytes4\"},{\"internalType\":\"bytes2\",\"name\":\"flag\",\"type\":\"bytes2\"},{\"internalType\":\"bytes\",\"name\":\"vin\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"vout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"witness\",\"type\":\"bytes\"},{\"internalType\":\"bytes4\",\"name\":\"locktime\",\"type\":\"bytes4\"}],\"indexed\":false,\"internalType\":\"struct Bridge.Transaction\",\"name\":\"payoutTx\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes4\",\"name\":\"outputId\",\"type\":\"bytes4\"}],\"indexed\":false,\"internalType\":\"struct Bridge.UTXO\",\"name\":\"spentUtxo\",\"type\":\"tuple\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"SafeWithdrawal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes4\",\"name\":\"outputId\",\"type\":\"bytes4\"}],\"indexed\":false,\"internalType\":\"struct Bridge.UTXO\",\"name\":\"utxo\",\"type\":\"tuple\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CODESEP_POS\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"EPOCH\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"INPUT_INDEX\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"KEY_VERSION\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"LIGHT_CLIENT\",\"outputs\":[{\"internalType\":\"contract BitcoinLightClient\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SCHNORR_VERIFIER_PRECOMPILE\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SIGHASH_ALL_HASH_TYPE\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SIGHASH_SINGLE_ANYONECANPAY_HASH_TYPE\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SPEND_TYPE_EXT\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SPEND_TYPE_NO_EXT\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SYSTEM_CALLER\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"txIds\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes4[]\",\"name\":\"outputIds\",\"type\":\"bytes4[]\"}],\"name\":\"batchWithdraw\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes4\",\"name\":\"version\",\"type\":\"bytes4\"},{\"internalType\":\"bytes2\",\"name\":\"flag\",\"type\":\"bytes2\"},{\"internalType\":\"bytes\",\"name\":\"vin\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"vout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"witness\",\"type\":\"bytes\"},{\"internalType\":\"bytes4\",\"name\":\"locktime\",\"type\":\"bytes4\"}],\"internalType\":\"struct Bridge.Transaction\",\"name\":\"moveTx\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes\",\"name\":\"intermediateNodes\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"blockHeight\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"internalType\":\"struct Bridge.MerkleProof\",\"name\":\"proof\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"shaScriptPubkeys\",\"type\":\"bytes32\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositPrefix\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositSuffix\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"depositTxIds\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failedDepositVault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAggregatedKey\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getWithdrawalCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"_depositPrefix\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_depositSuffix\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"_depositAmount\",\"type\":\"uint256\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initialized\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"processedTxIds\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes4\",\"name\":\"version\",\"type\":\"bytes4\"},{\"internalType\":\"bytes2\",\"name\":\"flag\",\"type\":\"bytes2\"},{\"internalType\":\"bytes\",\"name\":\"vin\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"vout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"witness\",\"type\":\"bytes\"},{\"internalType\":\"bytes4\",\"name\":\"locktime\",\"type\":\"bytes4\"}],\"internalType\":\"struct Bridge.Transaction\",\"name\":\"replaceTx\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes\",\"name\":\"intermediateNodes\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"blockHeight\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"internalType\":\"struct Bridge.MerkleProof\",\"name\":\"proof\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"idToReplace\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"shaScriptPubkeys\",\"type\":\"bytes32\"}],\"name\":\"replaceDeposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"replacePrefix\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"replaceSuffix\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes4\",\"name\":\"version\",\"type\":\"bytes4\"},{\"internalType\":\"bytes2\",\"name\":\"flag\",\"type\":\"bytes2\"},{\"internalType\":\"bytes\",\"name\":\"vin\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"vout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"witness\",\"type\":\"bytes\"},{\"internalType\":\"bytes4\",\"name\":\"locktime\",\"type\":\"bytes4\"}],\"internalType\":\"struct Bridge.Transaction\",\"name\":\"prepareTx\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes\",\"name\":\"intermediateNodes\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"blockHeight\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"internalType\":\"struct Bridge.MerkleProof\",\"name\":\"prepareProof\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes4\",\"name\":\"version\",\"type\":\"bytes4\"},{\"internalType\":\"bytes2\",\"name\":\"flag\",\"type\":\"bytes2\"},{\"internalType\":\"bytes\",\"name\":\"vin\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"vout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"witness\",\"type\":\"bytes\"},{\"internalType\":\"bytes4\",\"name\":\"locktime\",\"type\":\"bytes4\"}],\"internalType\":\"struct Bridge.Transaction\",\"name\":\"payoutTx\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"blockHeader\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"scriptPubKey\",\"type\":\"bytes\"}],\"name\":\"safeWithdraw\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"_depositPrefix\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_depositSuffix\",\"type\":\"bytes\"}],\"name\":\"setDepositScript\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_failedDepositVault\",\"type\":\"address\"}],\"name\":\"setFailedDepositVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"}],\"name\":\"setOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"_replacePrefix\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"_replaceSuffix\",\"type\":\"bytes\"}],\"name\":\"setReplaceScript\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes4\",\"name\":\"outputId\",\"type\":\"bytes4\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"withdrawalUTXOs\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"txId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes4\",\"name\":\"outputId\",\"type\":\"bytes4\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Citrea\",\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"acceptOwnership()\":{\"details\":\"The new owner accepts the ownership transfer.\"},\"batchWithdraw(bytes32[],bytes4[])\":{\"details\":\"Takes in multiple Bitcoin addresses as recipient addresses should be unique\",\"params\":{\"outputIds\":\"the outputIds of the outputs in the withdrawal transactions\",\"txIds\":\"the txIds of the withdrawal transactions on Bitcoin\"}},\"deposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),bytes32)\":{\"params\":{\"moveTx\":\"Transaction parameters of the move transaction on Bitcoin\",\"proof\":\"Merkle proof of the move transaction\",\"shaScriptPubkeys\":\"`shaScriptPubkeys` is the only component of the P2TR message hash that cannot be derived solely on the transaction itself in our case, as it requires knowledge of the previous transaction output that is being spent. Thus we calculate this component off-chain.\"}},\"getWithdrawalCount()\":{\"returns\":{\"_0\":\"The count of withdrawals happened so far\"}},\"initialize(bytes,bytes,uint256)\":{\"params\":{\"_depositAmount\":\"The CBTC amount that can be deposited and withdrawn\",\"_depositPrefix\":\"First part of the deposit script expected in the witness field for all L1 deposits \",\"_depositSuffix\":\"The suffix of the deposit script that follows the receiver address\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"pendingOwner()\":{\"details\":\"Returns the address of the pending owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"replaceDeposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),uint256,bytes32)\":{\"params\":{\"idToReplace\":\"The index of the deposit transaction to be replaced in the `depositTxIds` array\",\"proof\":\"Merkle proof of the replacement transaction\",\"replaceTx\":\"Transaction parameters of the replacement transaction on Bitcoin\",\"shaScriptPubkeys\":\"`shaScriptPubkeys` is the only component of the P2TR message hash that cannot be derived solely on the transaction itself in our case, as it requires knowledge of the previous transaction output that is being spent. Thus we calculate this component off-chain.\"}},\"safeWithdraw((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),(bytes4,bytes2,bytes,bytes,bytes,bytes4),bytes,bytes)\":{\"params\":{\"blockHeader\":\"Block header of the associated Bitcoin block\",\"payoutTx\":\"Transaction parameters of the payout transaction on Bitcoin\",\"prepareProof\":\"Merkle proof of the prepare transaction\",\"prepareTx\":\"Transaction parameters of the prepare transaction on Bitcoin\",\"scriptPubKey\":\"The script pubkey of the user, included for extra validation\"}},\"setDepositScript(bytes,bytes)\":{\"details\":\"Deposit script contains a fixed script that checks signatures of verifiers and pushes EVM address of the receiver\",\"params\":{\"_depositPrefix\":\"The new deposit script prefix\",\"_depositSuffix\":\"The part of the deposit script that succeeds the receiver address\"}},\"setFailedDepositVault(address)\":{\"params\":{\"_failedDepositVault\":\"The address of the failed deposit vault\"}},\"setOperator(address)\":{\"params\":{\"_operator\":\"Address of the privileged operator\"}},\"setReplaceScript(bytes,bytes)\":{\"details\":\"Replace script contains a fixed script that checks signatures of verifiers and pushes txId of the deposit transaction to be replaced\",\"params\":{\"_replacePrefix\":\"The new replace prefix\",\"_replaceSuffix\":\"The part of the replace script that succeeds the txId\"}},\"transferOwnership(address)\":{\"details\":\"Starts the ownership transfer of the contract to a new account. Replaces the pending transfer if there is one. Can only be called by the current owner.\"},\"withdraw(bytes32,bytes4)\":{\"params\":{\"outputId\":\"The outputId of the output in the withdrawal transaction\",\"txId\":\"The txId of the withdrawal transaction on Bitcoin\"}}},\"title\":\"Bridge contract for the Citrea end of Citrea <> Bitcoin bridge\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"batchWithdraw(bytes32[],bytes4[])\":{\"notice\":\"Batch version of `withdraw` that can accept multiple cBTC\"},\"deposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),bytes32)\":{\"notice\":\"Checks if the deposit amount is sent to the bridge multisig on Bitcoin, and if so, sends the deposit amount to the receiver\"},\"initialize(bytes,bytes,uint256)\":{\"notice\":\"Initializes the bridge contract and sets the deposit script\"},\"replaceDeposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),uint256,bytes32)\":{\"notice\":\"Operator can replace a deposit transaction with its replacement if the replacement transaction is included in Bitcoin and signed by N-of-N with the replacement script\"},\"safeWithdraw((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),(bytes4,bytes2,bytes,bytes,bytes,bytes4),bytes,bytes)\":{\"notice\":\"Same operation as `withdraw` with extra validations at the cost of gas. Validates the transactions, checks the inclusion of the transaction being spent and checks if the signature is valid.\"},\"setDepositScript(bytes,bytes)\":{\"notice\":\"Sets the expected deposit script of the deposit transaction on Bitcoin, contained in the witness\"},\"setFailedDepositVault(address)\":{\"notice\":\"Sets the address of the failed deposit vault\"},\"setOperator(address)\":{\"notice\":\"Sets the operator address that can process user deposits\"},\"setReplaceScript(bytes,bytes)\":{\"notice\":\"Sets the replace script of the replacement transaction on Bitcoin, contained in the witness\"},\"withdraw(bytes32,bytes4)\":{\"notice\":\"Accepts 1 cBTC from the sender and inserts this withdrawal request of 1 BTC on Bitcoin into the withdrawals array so that later on can be processed by the operator \"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Bridge.sol\":\"Bridge\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"appendCBOR\":false,\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":bitcoin-spv/=lib/bitcoin-spv/\",\":ds-test/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":openzeppelin/=lib/openzeppelin-contracts/contracts/\"],\"viaIR\":true},\"sources\":{\"lib/WitnessUtils.sol\":{\"keccak256\":\"0x0b0d59b4e73d6f5b4bbf1032f72bb15c2f1548c2ee319b80ae9e4c22576a70af\",\"license\":\"LGPL-3.0-or-later\",\"urls\":[\"bzz-raw://8499a5fc520941cb1b970637850cabfbc2d5a51abed824886063420c686b57de\",\"dweb:/ipfs/QmaLYLJ36PyFAaP3MgvFWW3knDsSUtVfCfs7Lp7oYFPZ1w\"]},\"lib/bitcoin-spv/solidity/contracts/BTCUtils.sol\":{\"keccak256\":\"0x439eaa97e9239705f3d31e8d39dccbad32311f1f119e295d53c65e0ae3c5a5fc\",\"urls\":[\"bzz-raw://976a361a89c21afc44b5e0a552271d9288b12cf34a9925c25f3c6975ece4e667\",\"dweb:/ipfs/QmNTb4eJyxV5iZj8RJGFBGSKXWsuvoMYqLLBgk16dhWePH\"]},\"lib/bitcoin-spv/solidity/contracts/BytesLib.sol\":{\"keccak256\":\"0x43e0f3b3b23c861bd031588bf410dfdd02e2af17941a89aa38d70e534e0380d1\",\"urls\":[\"bzz-raw://76011d699a8b229dbfdc698b3ece658daad9d96778e86d679aa576bc966209d6\",\"dweb:/ipfs/QmRZEWAeRQtsTUvfzEd1jb2wAqpTNR5KAme92gBRn4SYiT\"]},\"lib/bitcoin-spv/solidity/contracts/SafeMath.sol\":{\"keccak256\":\"0x35930d982394c7ffde439b82e5e696c5b21a6f09699d44861dfe409ef64084a3\",\"urls\":[\"bzz-raw://090e9d78755d4916fa2f5f5d8f9fd2fc59bfc5a25a5e91636a92c4c07aee9c6b\",\"dweb:/ipfs/QmXfz4TPDvgnuYz9eS5AL87GfCLxHQZJV1Y8ieJU9M8yTe\"]},\"lib/bitcoin-spv/solidity/contracts/ValidateSPV.sol\":{\"keccak256\":\"0xce3febbf3ad3a7ff8a8effd0c7ccaf7ccfa2719578b537d49ea196f0bae8062b\",\"urls\":[\"bzz-raw://5f18942483bf20507ae6c0abb5421df96b1aebb7af15f541bda8470f6277312a\",\"dweb:/ipfs/QmPzEpA8w5k6pVFadm3UCLqNdxFAjPwP9Lpi5HMQsQg52J\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/Ownable2StepUpgradeable.sol\":{\"keccak256\":\"0xbca4a4f66d98028293dba695851d1b20d3e0ba2fff7453fb241f192fa3fc6b6f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://013b3cfd9d1e34dad409c3b9a340860e8651e61cda509de33599fb5102f62fe7\",\"dweb:/ipfs/QmTVjDKofM9Nst8w8LAA3HHgi1eCnGYBpFb7Nbat71e2xz\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"src/BitcoinLightClient.sol\":{\"keccak256\":\"0x480b7e0492d955afc75a48edf467580700de2d736b91061fedfbdccaee421b92\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1d337385dbf93792536f5fa83258f4bcc7d1bc57c971214d692a374a21f4230e\",\"dweb:/ipfs/QmSwZ9nDUJtUL9EvWPX3oWEWBFnuwvUw6WWqgqABZmKmxm\"]},\"src/Bridge.sol\":{\"keccak256\":\"0xd04b831319a257ca187779341cd11f296c75451f5845610ebf538aa1f5004dd5\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://49df8d7539005d014bbf085b5aa6eab8476c1b02f347b2863b71ae48636b2f5c\",\"dweb:/ipfs/QmQZdDqFYcLmqrhSRGUP2YDAxw2sAZiJbSXxXZXw5zMVY7\"]},\"src/interfaces/IBitcoinLightClient.sol\":{\"keccak256\":\"0xc2c31dad4bb43601935c6226efd6d9ad6f38fdd9e57f6cb7c4ec609ae1f220e5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1c95da69bee1725b91079ef1af4a5b405f98a69cf82aa2009db683689c3fd1eb\",\"dweb:/ipfs/QmPweYQF1xUexHiaqKZwEv5THdUPhmdGNcvYvw8k4bazkA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "bytes32", "name": "wtxId", "type": "bytes32", "indexed": false}, {"internalType": "bytes32", "name": "txId", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "timestamp", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositId", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256", "indexed": false}, {"internalType": "bytes32", "name": "oldTxId", "type": "bytes32", "indexed": false}, {"internalType": "bytes32", "name": "newTxId", "type": "bytes32", "indexed": false}], "type": "event", "name": "DepositReplaced", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "depositPrefix", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "depositSuffix", "type": "bytes", "indexed": false}], "type": "event", "name": "DepositScriptUpdate", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "wtxId", "type": "bytes32", "indexed": false}, {"internalType": "bytes32", "name": "txId", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "timestamp", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositId", "type": "uint256", "indexed": false}], "type": "event", "name": "DepositTransferFailed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldVault", "type": "address", "indexed": false}, {"internalType": "address", "name": "newVault", "type": "address", "indexed": false}], "type": "event", "name": "FailedDepositVaultUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "oldOperator", "type": "address", "indexed": false}, {"internalType": "address", "name": "newOperator", "type": "address", "indexed": false}], "type": "event", "name": "OperatorUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferStarted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "replacePrefix", "type": "bytes", "indexed": false}, {"internalType": "bytes", "name": "replaceSuffix", "type": "bytes", "indexed": false}], "type": "event", "name": "ReplaceScriptUpdate", "anonymous": false}, {"inputs": [{"internalType": "struct Bridge.Transaction", "name": "payoutTx", "type": "tuple", "components": [{"internalType": "bytes4", "name": "version", "type": "bytes4"}, {"internalType": "bytes2", "name": "flag", "type": "bytes2"}, {"internalType": "bytes", "name": "vin", "type": "bytes"}, {"internalType": "bytes", "name": "vout", "type": "bytes"}, {"internalType": "bytes", "name": "witness", "type": "bytes"}, {"internalType": "bytes4", "name": "locktime", "type": "bytes4"}], "indexed": false}, {"internalType": "struct Bridge.UTXO", "name": "spentUtxo", "type": "tuple", "components": [{"internalType": "bytes32", "name": "txId", "type": "bytes32"}, {"internalType": "bytes4", "name": "outputId", "type": "bytes4"}], "indexed": false}, {"internalType": "uint256", "name": "index", "type": "uint256", "indexed": false}], "type": "event", "name": "SafeWithdrawal", "anonymous": false}, {"inputs": [{"internalType": "struct Bridge.UTXO", "name": "utxo", "type": "tuple", "components": [{"internalType": "bytes32", "name": "txId", "type": "bytes32"}, {"internalType": "bytes4", "name": "outputId", "type": "bytes4"}], "indexed": false}, {"internalType": "uint256", "name": "index", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "timestamp", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CODESEP_POS", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "EPOCH", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "INPUT_INDEX", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "KEY_VERSION", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "LIGHT_CLIENT", "outputs": [{"internalType": "contract BitcoinLightClient", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SCHNORR_VERIFIER_PRECOMPILE", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SIGHAS<PERSON>_ALL_HASH_TYPE", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SIGHASH_SINGLE_ANYONECANPAY_HASH_TYPE", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SPEND_TYPE_EXT", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SPEND_TYPE_NO_EXT", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SYSTEM_CALLER", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptOwnership"}, {"inputs": [{"internalType": "bytes32[]", "name": "txIds", "type": "bytes32[]"}, {"internalType": "bytes4[]", "name": "outputIds", "type": "bytes4[]"}], "stateMutability": "payable", "type": "function", "name": "batchWithdraw"}, {"inputs": [{"internalType": "struct Bridge.Transaction", "name": "moveTx", "type": "tuple", "components": [{"internalType": "bytes4", "name": "version", "type": "bytes4"}, {"internalType": "bytes2", "name": "flag", "type": "bytes2"}, {"internalType": "bytes", "name": "vin", "type": "bytes"}, {"internalType": "bytes", "name": "vout", "type": "bytes"}, {"internalType": "bytes", "name": "witness", "type": "bytes"}, {"internalType": "bytes4", "name": "locktime", "type": "bytes4"}]}, {"internalType": "struct Bridge.MerkleProof", "name": "proof", "type": "tuple", "components": [{"internalType": "bytes", "name": "intermediateNodes", "type": "bytes"}, {"internalType": "uint256", "name": "blockHeight", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}]}, {"internalType": "bytes32", "name": "shaScriptPubkeys", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositPrefix", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositSuffix", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "depositTxIds", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failedDepositVault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAggregatedKey", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getWithdrawalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "_depositPrefix", "type": "bytes"}, {"internalType": "bytes", "name": "_depositSuffix", "type": "bytes"}, {"internalType": "uint256", "name": "_depositAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "initialized", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pending<PERSON><PERSON>er", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "processedTxIds", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "struct Bridge.Transaction", "name": "replaceTx", "type": "tuple", "components": [{"internalType": "bytes4", "name": "version", "type": "bytes4"}, {"internalType": "bytes2", "name": "flag", "type": "bytes2"}, {"internalType": "bytes", "name": "vin", "type": "bytes"}, {"internalType": "bytes", "name": "vout", "type": "bytes"}, {"internalType": "bytes", "name": "witness", "type": "bytes"}, {"internalType": "bytes4", "name": "locktime", "type": "bytes4"}]}, {"internalType": "struct Bridge.MerkleProof", "name": "proof", "type": "tuple", "components": [{"internalType": "bytes", "name": "intermediateNodes", "type": "bytes"}, {"internalType": "uint256", "name": "blockHeight", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}]}, {"internalType": "uint256", "name": "idToReplace", "type": "uint256"}, {"internalType": "bytes32", "name": "shaScriptPubkeys", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "replaceDeposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "replacePrefix", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "replaceSuffix", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "struct Bridge.Transaction", "name": "prepareTx", "type": "tuple", "components": [{"internalType": "bytes4", "name": "version", "type": "bytes4"}, {"internalType": "bytes2", "name": "flag", "type": "bytes2"}, {"internalType": "bytes", "name": "vin", "type": "bytes"}, {"internalType": "bytes", "name": "vout", "type": "bytes"}, {"internalType": "bytes", "name": "witness", "type": "bytes"}, {"internalType": "bytes4", "name": "locktime", "type": "bytes4"}]}, {"internalType": "struct Bridge.MerkleProof", "name": "prepareProof", "type": "tuple", "components": [{"internalType": "bytes", "name": "intermediateNodes", "type": "bytes"}, {"internalType": "uint256", "name": "blockHeight", "type": "uint256"}, {"internalType": "uint256", "name": "index", "type": "uint256"}]}, {"internalType": "struct Bridge.Transaction", "name": "payoutTx", "type": "tuple", "components": [{"internalType": "bytes4", "name": "version", "type": "bytes4"}, {"internalType": "bytes2", "name": "flag", "type": "bytes2"}, {"internalType": "bytes", "name": "vin", "type": "bytes"}, {"internalType": "bytes", "name": "vout", "type": "bytes"}, {"internalType": "bytes", "name": "witness", "type": "bytes"}, {"internalType": "bytes4", "name": "locktime", "type": "bytes4"}]}, {"internalType": "bytes", "name": "blockHeader", "type": "bytes"}, {"internalType": "bytes", "name": "script<PERSON>ub<PERSON><PERSON>", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "safeWithdraw"}, {"inputs": [{"internalType": "bytes", "name": "_depositPrefix", "type": "bytes"}, {"internalType": "bytes", "name": "_depositSuffix", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "setDepositScript"}, {"inputs": [{"internalType": "address", "name": "_failedDepositVault", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFailedDepositVault"}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator"}, {"inputs": [{"internalType": "bytes", "name": "_replacePrefix", "type": "bytes"}, {"internalType": "bytes", "name": "_replaceSuffix", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "setReplaceScript"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "bytes32", "name": "txId", "type": "bytes32"}, {"internalType": "bytes4", "name": "outputId", "type": "bytes4"}], "stateMutability": "payable", "type": "function", "name": "withdraw"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "withdrawalUTXOs", "outputs": [{"internalType": "bytes32", "name": "txId", "type": "bytes32"}, {"internalType": "bytes4", "name": "outputId", "type": "bytes4"}]}], "devdoc": {"kind": "dev", "methods": {"acceptOwnership()": {"details": "The new owner accepts the ownership transfer."}, "batchWithdraw(bytes32[],bytes4[])": {"details": "Takes in multiple Bitcoin addresses as recipient addresses should be unique", "params": {"outputIds": "the outputIds of the outputs in the withdrawal transactions", "txIds": "the txIds of the withdrawal transactions on Bitcoin"}}, "deposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),bytes32)": {"params": {"moveTx": "Transaction parameters of the move transaction on Bitcoin", "proof": "Merkle proof of the move transaction", "shaScriptPubkeys": "`shaScriptPubkeys` is the only component of the P2TR message hash that cannot be derived solely on the transaction itself in our case, as it requires knowledge of the previous transaction output that is being spent. Thus we calculate this component off-chain."}}, "getWithdrawalCount()": {"returns": {"_0": "The count of withdrawals happened so far"}}, "initialize(bytes,bytes,uint256)": {"params": {"_depositAmount": "The CBTC amount that can be deposited and withdrawn", "_depositPrefix": "First part of the deposit script expected in the witness field for all L1 deposits ", "_depositSuffix": "The suffix of the deposit script that follows the receiver address"}}, "owner()": {"details": "Returns the address of the current owner."}, "pendingOwner()": {"details": "Returns the address of the pending owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "replaceDeposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),uint256,bytes32)": {"params": {"idToReplace": "The index of the deposit transaction to be replaced in the `depositTxIds` array", "proof": "Me<PERSON>le proof of the replacement transaction", "replaceTx": "Transaction parameters of the replacement transaction on Bitcoin", "shaScriptPubkeys": "`shaScriptPubkeys` is the only component of the P2TR message hash that cannot be derived solely on the transaction itself in our case, as it requires knowledge of the previous transaction output that is being spent. Thus we calculate this component off-chain."}}, "safeWithdraw((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),(bytes4,bytes2,bytes,bytes,bytes,bytes4),bytes,bytes)": {"params": {"blockHeader": "Block header of the associated Bitcoin block", "payoutTx": "Transaction parameters of the payout transaction on Bitcoin", "prepareProof": "Me<PERSON>le proof of the prepare transaction", "prepareTx": "Transaction parameters of the prepare transaction on Bitcoin", "scriptPubKey": "The script pubkey of the user, included for extra validation"}}, "setDepositScript(bytes,bytes)": {"details": "Deposit script contains a fixed script that checks signatures of verifiers and pushes EVM address of the receiver", "params": {"_depositPrefix": "The new deposit script prefix", "_depositSuffix": "The part of the deposit script that succeeds the receiver address"}}, "setFailedDepositVault(address)": {"params": {"_failedDepositVault": "The address of the failed deposit vault"}}, "setOperator(address)": {"params": {"_operator": "Address of the privileged operator"}}, "setReplaceScript(bytes,bytes)": {"details": "Replace script contains a fixed script that checks signatures of verifiers and pushes txId of the deposit transaction to be replaced", "params": {"_replacePrefix": "The new replace prefix", "_replaceSuffix": "The part of the replace script that succeeds the txId"}}, "transferOwnership(address)": {"details": "Starts the ownership transfer of the contract to a new account. Replaces the pending transfer if there is one. Can only be called by the current owner."}, "withdraw(bytes32,bytes4)": {"params": {"outputId": "The outputId of the output in the withdrawal transaction", "txId": "The txId of the withdrawal transaction on Bitcoin"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"batchWithdraw(bytes32[],bytes4[])": {"notice": "Batch version of `withdraw` that can accept multiple cBTC"}, "deposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),bytes32)": {"notice": "Checks if the deposit amount is sent to the bridge multisig on Bitcoin, and if so, sends the deposit amount to the receiver"}, "initialize(bytes,bytes,uint256)": {"notice": "Initializes the bridge contract and sets the deposit script"}, "replaceDeposit((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),uint256,bytes32)": {"notice": "Operator can replace a deposit transaction with its replacement if the replacement transaction is included in Bitcoin and signed by N-of-N with the replacement script"}, "safeWithdraw((bytes4,bytes2,bytes,bytes,bytes,bytes4),(bytes,uint256,uint256),(bytes4,bytes2,bytes,bytes,bytes,bytes4),bytes,bytes)": {"notice": "Same operation as `withdraw` with extra validations at the cost of gas. Validates the transactions, checks the inclusion of the transaction being spent and checks if the signature is valid."}, "setDepositScript(bytes,bytes)": {"notice": "Sets the expected deposit script of the deposit transaction on Bitcoin, contained in the witness"}, "setFailedDepositVault(address)": {"notice": "Sets the address of the failed deposit vault"}, "setOperator(address)": {"notice": "Sets the operator address that can process user deposits"}, "setReplaceScript(bytes,bytes)": {"notice": "Sets the replace script of the replacement transaction on Bitcoin, contained in the witness"}, "withdraw(bytes32,bytes4)": {"notice": "Accepts 1 cBTC from the sender and inserts this withdrawal request of 1 BTC on Bitcoin into the withdrawals array so that later on can be processed by the operator "}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "bitcoin-spv/=lib/bitcoin-spv/", "ds-test/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "openzeppelin/=lib/openzeppelin-contracts/contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none", "appendCBOR": false}, "compilationTarget": {"src/Bridge.sol": "Bridge"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/WitnessUtils.sol": {"keccak256": "0x0b0d59b4e73d6f5b4bbf1032f72bb15c2f1548c2ee319b80ae9e4c22576a70af", "urls": ["bzz-raw://8499a5fc520941cb1b970637850cabfbc2d5a51abed824886063420c686b57de", "dweb:/ipfs/QmaLYLJ36PyFAaP3MgvFWW3knDsSUtVfCfs7Lp7oYFPZ1w"], "license": "LGPL-3.0-or-later"}, "lib/bitcoin-spv/solidity/contracts/BTCUtils.sol": {"keccak256": "0x439eaa97e9239705f3d31e8d39dccbad32311f1f119e295d53c65e0ae3c5a5fc", "urls": ["bzz-raw://976a361a89c21afc44b5e0a552271d9288b12cf34a9925c25f3c6975ece4e667", "dweb:/ipfs/QmNTb4eJyxV5iZj8RJGFBGSKXWsuvoMYqLLBgk16dhWePH"], "license": null}, "lib/bitcoin-spv/solidity/contracts/BytesLib.sol": {"keccak256": "0x43e0f3b3b23c861bd031588bf410dfdd02e2af17941a89aa38d70e534e0380d1", "urls": ["bzz-raw://76011d699a8b229dbfdc698b3ece658daad9d96778e86d679aa576bc966209d6", "dweb:/ipfs/QmRZEWAeRQtsTUvfzEd1jb2wAqpTNR5KAme92gBRn4SYiT"], "license": null}, "lib/bitcoin-spv/solidity/contracts/SafeMath.sol": {"keccak256": "0x35930d982394c7ffde439b82e5e696c5b21a6f09699d44861dfe409ef64084a3", "urls": ["bzz-raw://090e9d78755d4916fa2f5f5d8f9fd2fc59bfc5a25a5e91636a92c4c07aee9c6b", "dweb:/ipfs/QmXfz4TPDvgnuYz9eS5AL87GfCLxHQZJV1Y8ieJU9M8yTe"], "license": null}, "lib/bitcoin-spv/solidity/contracts/ValidateSPV.sol": {"keccak256": "0xce3febbf3ad3a7ff8a8effd0c7ccaf7ccfa2719578b537d49ea196f0bae8062b", "urls": ["bzz-raw://5f18942483bf20507ae6c0abb5421df96b1aebb7af15f541bda8470f6277312a", "dweb:/ipfs/QmPzEpA8w5k6pVFadm3UCLqNdxFAjPwP9Lpi5HMQsQg52J"], "license": null}, "lib/openzeppelin-contracts-upgradeable/contracts/access/Ownable2StepUpgradeable.sol": {"keccak256": "0xbca4a4f66d98028293dba695851d1b20d3e0ba2fff7453fb241f192fa3fc6b6f", "urls": ["bzz-raw://013b3cfd9d1e34dad409c3b9a340860e8651e61cda509de33599fb5102f62fe7", "dweb:/ipfs/QmTVjDKofM9Nst8w8LAA3HHgi1eCnGYBpFb7Nbat71e2xz"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "src/BitcoinLightClient.sol": {"keccak256": "0x480b7e0492d955afc75a48edf467580700de2d736b91061fedfbdccaee421b92", "urls": ["bzz-raw://1d337385dbf93792536f5fa83258f4bcc7d1bc57c971214d692a374a21f4230e", "dweb:/ipfs/QmSwZ9nDUJtUL9EvWPX3oWEWBFnuwvUw6WWqgqABZmKmxm"], "license": "MIT"}, "src/Bridge.sol": {"keccak256": "0xd04b831319a257ca187779341cd11f296c75451f5845610ebf538aa1f5004dd5", "urls": ["bzz-raw://49df8d7539005d014bbf085b5aa6eab8476c1b02f347b2863b71ae48636b2f5c", "dweb:/ipfs/QmQZdDqFYcLmqrhSRGUP2YDAxw2sAZiJbSXxXZXw5zMVY7"], "license": "UNLICENSED"}, "src/interfaces/IBitcoinLightClient.sol": {"keccak256": "0xc2c31dad4bb43601935c6226efd6d9ad6f38fdd9e57f6cb7c4ec609ae1f220e5", "urls": ["bzz-raw://1c95da69bee1725b91079ef1af4a5b405f98a69cf82aa2009db683689c3fd1eb", "dweb:/ipfs/QmPweYQF1xUexHiaqKZwEv5THdUPhmdGNcvYvw8k4bazkA"], "license": "MIT"}}, "version": 1}, "id": 10}