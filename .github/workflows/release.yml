name: release

on:
  push:
    tags:
      - "v*.*.*"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ (github.ref != 'refs/heads/main') }}

jobs:
  linux_amd64_binary_extraction:
    runs-on: ubicloud-standard-30
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Dependencies
        run: |
          sudo apt update && sudo apt -y install curl gcc cpp cmake clang llvm
          sudo apt -y autoremove && sudo apt clean && sudo rm -rf /var/lib/apt/lists/*

      - name: Install Rust
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          rustup install 1.85.0
          rustup default 1.85.0

      - name: Install risc0
        uses: ./.github/actions/test-prerequisites
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Project (No Features)
        run: |
          cargo build --release

      - name: Copy No Features Binaries
        run: |
          cp target/release/clementine-core target/release/clementine-core-no-automation
          cp target/release/clementine-cli target/release/clementine-cli-no-automation

      - name: Build Project (with automation)
        run: |
          cargo build --features automation --release

      - name: Copy Automation Feature Binaries
        run: |
          cp target/release/clementine-core target/release/clementine-core-with-automation
          cp target/release/clementine-cli target/release/clementine-cli-with-automation

      - name: Upload clementine no-automation linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: clementine-core-${{ github.ref_name }}-no-automation-linux-amd64
          path: target/release/clementine-core-no-automation

      - name: Upload clementine with-automation linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: clementine-core-${{ github.ref_name }}-with-automation-linux-amd64
          path: target/release/clementine-core-with-automation

      - name: Upload clementine-cli no-automation linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: clementine-cli-${{ github.ref_name }}-no-automation-linux-amd64
          path: target/release/clementine-cli-no-automation

      - name: Upload clementine-cli with-automation linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: clementine-cli-${{ github.ref_name }}-with-automation-linux-amd64
          path: target/release/clementine-cli-with-automation

  release:
    needs: linux_amd64_binary_extraction
    runs-on: ubicloud-standard-2
    steps:
      - name: Download no-automation linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: clementine-core-${{ github.ref_name }}-no-automation-linux-amd64
          path: release

      - name: rename no-automation file
        run: |
          mv release/clementine-core-no-automation release/clementine-core-${{ github.ref_name }}-no-automation-linux-amd64

      - name: Download with-automation linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: clementine-core-${{ github.ref_name }}-with-automation-linux-amd64
          path: release

      - name: rename with-automation file
        run: |
          mv release/clementine-core-with-automation release/clementine-core-${{ github.ref_name }}-with-automation-linux-amd64

      - name: Download clementine-cli no-automation linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: clementine-cli-${{ github.ref_name }}-no-automation-linux-amd64
          path: release

      - name: rename clementine-cli no-automation file
        run: |
          mv release/clementine-cli-no-automation release/clementine-cli-${{ github.ref_name }}-no-automation-linux-amd64

      - name: Download clementine-cli with-automation linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: clementine-cli-${{ github.ref_name }}-with-automation-linux-amd64
          path: release

      - name: rename clementine-cli with-automation file
        run: |
          mv release/clementine-cli-with-automation release/clementine-cli-${{ github.ref_name }}-with-automation-linux-amd64

      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          draft: ${{ contains(github.ref, 'tmp') }}
          files: |
            release/clementine-core-${{ github.ref_name }}-no-automation-linux-amd64
            release/clementine-core-${{ github.ref_name }}-with-automation-linux-amd64
            release/clementine-cli-${{ github.ref_name }}-no-automation-linux-amd64
            release/clementine-cli-${{ github.ref_name }}-with-automation-linux-amd64
          name: Release ${{ github.ref_name }}
          body: |
            This is the release for version ${{ github.ref_name }}.

            It includes:
            - clementine-core-${{ github.ref_name }}-no-automation-linux-amd64
            - clementine-core-${{ github.ref_name }}-with-automation-linux-amd64
            - clementine-cli-${{ github.ref_name }}-no-automation-linux-amd64
            - clementine-cli-${{ github.ref_name }}-with-automation-linux-amd64

      - uses: actions/checkout@v4
      - name: Docker Setup Buildx
        uses: docker/setup-buildx-action@v3.2.0
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v5.3.0
        with:
          context: .
          file: ./scripts/docker/Dockerfile
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/clementine:${{ github.ref_name }}
