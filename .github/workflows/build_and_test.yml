name: Build And Test

on:
  push:
    branches:
      - main
      - "releases/*"
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ (github.ref != 'refs/heads/main') }}

env:
  CARGO_TERM_COLOR: always
  RUST_LOG: warn,risc0_zkvm=error,risc0_circuit_rv32im=error
  RISC0_DEV_MODE: 1
  RUST_MIN_STACK: 33554432

  CARGOFLAGS: --workspace --all-targets
  CARGOFLAGS_ALL_FEATURES: --workspace --all-targets --all-features

jobs:
  # Build ----------------------------------------------------------------------
  release_build_all_features:
    name: Release | All features | Compile
    runs-on: ubicloud-standard-16
    if: ${{ !github.event.pull_request.draft }}

    steps:
      - uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-prerequisites

      - name: Save/restore build artifacts
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo-RELEASE-${{ hashFiles('**/Cargo.lock') }}-${{ github.sha }}

      - name: Compile in release mode
        run: cargo build $CARGOFLAGS_ALL_FEATURES --release

  release_build:
    name: Release | No features | Compile
    runs-on: ubicloud-standard-2
    if: ${{ !github.event.pull_request.draft }}

    steps:
      - uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4

      - name: Compile in release mode
        run: cargo build $CARGOFLAGS --release

  # Full Tests -----------------------------------------------------------------
  debug_build_test_all_features:
    name: Debug | All features | Test
    runs-on: ubicloud-standard-16

    env:
      INFO_LOG_FILE: ${{ github.workspace }}/test-logs/debug/debug-all-features-test.log

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: clementine
          POSTGRES_USER: clementine
          POSTGRES_PASSWORD: clementine
          POSTGRES_INITDB_ARGS: "-c shared_buffers=8GB -c max_connections=5000"
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-prerequisites
      - uses: ./.github/actions/test-prerequisites
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create test log directories
        run: mkdir -p test-logs/debug

      - name: Save/restore build artifacts
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo-DEBUG-${{ hashFiles('**/Cargo.lock') }}-${{ github.sha }}

      - name: Run unit tests
        id: unit_tests
        run: |
          set -o pipefail
          ./scripts/generate_certs.sh
          cargo test_unit 2>&1 | tee unit_test_output.log

      - name: Check for specific test failure and run generate_deposit_state
        if: failure()
        run: |
          if grep -q "test builder::sighash::tests::test_bridge_contract_change ... FAILED" unit_test_output.log; then
            echo "Found test_bridge_contract_change failure, running generate_deposit_state test"
            cargo test generate_deposit_state $CARGOFLAGS_ALL_FEATURES -- --ignored
          fi

      - name: Upload deposit state artifact
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: deposit-state-debug
          path: core/src/test/data/deposit_state_debug.bincode
          if-no-files-found: ignore
          retention-days: 1

      - name: Run integration tests
        run: cargo test_integration

      - name: Upload test logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: debug-test-logs
          path: test-logs/debug/
          retention-days: 7

  release_build_test_all_features:
    name: Release | All features | Test
    runs-on: ubicloud-standard-16
    if: ${{ !github.event.pull_request.draft }}
    needs: release_build_all_features

    env:
      INFO_LOG_FILE: ${{ github.workspace }}/test-logs/release/release-all-features-test.log

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: clementine
          POSTGRES_USER: clementine
          POSTGRES_PASSWORD: clementine
          POSTGRES_INITDB_ARGS: "-c shared_buffers=8GB -c max_connections=5000"
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-prerequisites
      - uses: ./.github/actions/test-prerequisites
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create test log directories
        run: mkdir -p test-logs/release

      - name: Save/restore build artifacts
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo-RELEASE-${{ hashFiles('**/Cargo.lock') }}-${{ github.sha }}

      - name: Run unit tests
        id: unit_tests
        run: |
          set -o pipefail
          ./scripts/generate_certs.sh
          cargo test_unit --release 2>&1 | tee unit_test_output.log

      - name: Check for specific test failure and run generate_deposit_state
        if: failure()
        run: |
          if grep -q "test builder::sighash::tests::test_bridge_contract_change ... FAILED" unit_test_output.log; then
            echo "Found test_bridge_contract_change failure, running generate_deposit_state test"
            cargo test --release generate_deposit_state $CARGOFLAGS_ALL_FEATURES -- --ignored
          fi

      - name: Upload deposit state artifact
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: deposit-state-release
          path: core/src/test/data/deposit_state_release.bincode
          if-no-files-found: ignore
          retention-days: 1

      - name: Run integration tests
        run: cargo test_integration_release

      - name: Upload test logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: release-test-logs
          path: test-logs/release/
          retention-days: 7

  # Matrix (Standalone) Tests --------------------------------------------------
  standalone_tests:
    name: Release | All features | Test ${{ matrix.description_suffix }}
    runs-on: ubicloud-standard-8
    if: ${{ !github.event.pull_request.draft }}
    needs: release_build_all_features

    env:
      INFO_LOG_FILE: ${{ github.workspace }}/test-logs/standalone/standalone-${{ matrix.test_name }}-test.log

    strategy:
      fail-fast: false
      matrix:
        include:
          - test_script_name: additional_disprove_script_test_disrupted_payout_tx_block_hash
            description_suffix: "Add. disprove disrupted payout tx blockhash path"
            test_name: "additional-disrupted-payout"
          - test_script_name: additional_disprove_script_test_disrupt_chal_sending_wts
            description_suffix: "Add. disprove disrupted challenge sending watchtowers path"
            test_name: "additional-disrupted-chal-sending-wts"
          - test_script_name: additional_disprove_script_test_operator_forgot_wt_challenge
            description_suffix: "Add. disprove operator forgot watchtower challenge path"
            test_name: "additional-forgot-challenge"
          - test_script_name: additional_disprove_script_test_disrupted_latest_block_hash
            description_suffix: "Add. disprove disrupted latest blockhash path"
            test_name: "additional-disrupted-latest-blockhash"
          - test_script_name: additional_disprove_script_test_corrupted_public_input
            description_suffix: "Add. disprove disrupted public input path"
            test_name: "additional-corrupted-public-input"
          - test_script_name: bitvm_disprove_scripts::disprove_script_test_healthy
            description_suffix: "Disprove healthy path"
            test_name: "disprove-healthy"
          - test_script_name: bitvm_disprove_scripts::disprove_script_test_corrupted_assert
            description_suffix: "Disprove disrupted assert path"
            test_name: "disprove-corrupted-assert"
          - test_script_name: citrea_deposit_and_withdraw_e2e_non_zero_genesis_height
            description_suffix: "E2E non-zero genesis height"
            test_name: "citrea-deposit-and-withdraw-non-zero-genesis-height"

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: clementine
          POSTGRES_USER: clementine
          POSTGRES_PASSWORD: clementine
          POSTGRES_INITDB_ARGS: "-c shared_buffers=8GB -c max_connections=1000"
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-prerequisites
      - uses: ./.github/actions/test-prerequisites
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create test log directories
        run: mkdir -p test-logs/standalone

      - name: Save/restore build artifacts
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo-RELEASE-${{ hashFiles('**/Cargo.lock') }}-${{ github.sha }}

      - name: Run tests
        run: |
          ./scripts/generate_certs.sh
          cargo test ${{ matrix.test_script_name }} $CARGOFLAGS_ALL_FEATURES --release -- --ignored --nocapture

      - name: Upload test logs
        if: failure() || cancelled()
        uses: actions/upload-artifact@v4
        with:
          name: standalone-test-logs-${{ matrix.test_name }}
          path: test-logs/standalone/
          retention-days: 7
