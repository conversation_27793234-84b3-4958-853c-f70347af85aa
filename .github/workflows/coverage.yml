name: Run Coverage

on: [workflow_dispatch]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ (github.ref != 'refs/heads/main') }}

env:
  CARGO_TERM_COLOR: always
  RUST_LOG: warn,risc0_zkvm=error,risc0_circuit_rv32im=error
  RISC0_DEV_MODE: 1
  RUST_MIN_STACK: 33554432

  CARGOFLAGS: --workspace --all-targets
  CARGOFLAGS_ALL_FEATURES: --workspace --all-targets --all-features

jobs:
  coverage:
    name: Release | All features | Coverage
    runs-on: ubicloud-standard-30

    env:
      INFO_LOG_FILE: ${{ github.workspace }}/test-logs/coverage/coverage-test.log

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: clementine
          POSTGRES_USER: clementine
          POSTGRES_PASSWORD: clementine
          POSTGRES_INITDB_ARGS: "-c shared_buffers=8GB -c max_connections=5000"
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          comment_on_pr: false

      - uses: actions/checkout@v4
      - uses: ./.github/actions/build-prerequisites
      - uses: ./.github/actions/test-prerequisites
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - uses: taiki-e/install-action@cargo-llvm-cov

      - name: Create test log directories
        run: mkdir -p test-logs/coverage

      - name: Save/restore build artifacts
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo-RELEASE-${{ hashFiles('**/Cargo.lock') }}-${{ github.sha }}

      - name: Run coverage
        run: cargo llvm-cov $CARGOFLAGS_ALL_FEATURES --release --lcov --locked --output-path lcov.info -- --test-threads 6

      - name: Upload coverage
        uses: codecov/codecov-action@v4
        with:
          fail_ci_if_error: true # optional (default = false)
          files: ./lcov.info
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Upload test logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-test-logs
          path: test-logs/coverage/
          retention-days: 7
