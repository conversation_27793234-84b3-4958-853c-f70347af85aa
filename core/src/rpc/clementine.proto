syntax = "proto3";
package clementine;

message Empty {}

message Txid {
  bytes txid = 1;
}

message Outpoint {
  Txid txid = 1;
  uint32 vout = 2;
}

message NofnResponse {
  bytes nofn_xonly_pk = 1;
  uint32 num_verifiers = 2;
}

enum NormalSignatureKind {
  NormalSignatureUnknown = 0;
  // Used for TxHandlers that verifiers don't care. These will have signatures created
  // by the operator on the fly.
  OperatorSighashDefault = 1;
  Challenge = 2;
  DisproveTimeout2 = 3;
  Disprove2 = 4;
  Reimburse1 = 5;
  KickoffNotFinalized1 = 6;
  KickoffNotFinalized2 = 7;
  Reimburse2 = 8;
  NoSignature = 9;
  ChallengeTimeout2 = 10;
  MiniAssert1 = 11;
  OperatorChallengeAck1 = 12;
  NotStored = 13;
  YieldKickoffTxid = 14;
  LatestBlockhashTimeout1 = 15;
  LatestBlockhashTimeout2 = 16;
  LatestBlockhashTimeout3 = 17;
  LatestBlockhash = 18;
}

// Signatures that are needed multiple times per an operators kickoff.
// Some watchtower sigs are needed once per watchtower.
// Asserts are needed multiple times
enum NumberedSignatureKind {
  NumberedSignatureUnknown = 0;
  // Used for TxHandlers that verifiers don't care. These will have signatures created
  // by the operator on the fly.
  NumberedNotStored = 1;
  OperatorChallengeNack1 = 2;
  OperatorChallengeNack2 = 3;
  OperatorChallengeNack3 = 4;
  AssertTimeout1 = 5;
  AssertTimeout2 = 6;
  AssertTimeout3 = 7;
  UnspentKickoff1 = 8;
  UnspentKickoff2 = 9;
  WatchtowerChallengeTimeout1 = 10;
  WatchtowerChallengeTimeout2 = 11;
  WatchtowerChallenge = 12;
}

message NormalSignatureId {
  NormalSignatureKind signature_kind = 1;
}

message NumberedSignatureId {
  NumberedSignatureKind signature_kind = 1;
  int32 idx = 2;
}

// A tagged signature struct that identifies the transaction-input that the signature is for.
// The id is left as NotStored for signatures that are created on the fly by the operator (they're also not stored).
message TaggedSignature {
  oneof signature_id {
    NormalSignatureId normal_signature = 1;
    NumberedSignatureId numbered_signature = 2;
  }
  bytes signature = 3;
}

message DepositSignatures { repeated TaggedSignature signatures = 1; }

message ChallengeACKDigest {
  bytes hash = 1;
}

message WinternitzPubkey {
  repeated bytes digit_pubkey = 3;
}

message DepositParams {
  Deposit deposit = 1;
  Actors actors = 2;
  SecurityCouncil security_council = 3;
}

message SecurityCouncil {
  repeated bytes pks = 1;
  uint32 threshold = 2;
}

message Deposit {
  /// User's deposit UTXO.
  Outpoint deposit_outpoint = 1;
  oneof deposit_data {
    BaseDeposit base_deposit = 2;
    ReplacementDeposit replacement_deposit = 3;
  }
}

message Actors {
  /// Public keys of verifiers that will participate in the deposit.
  VerifierPublicKeys verifiers = 1;
  /// X-only public keys of watchtowers that will participate in the deposit.
  /// NOTE: verifiers are automatically considered watchtowers. This field is only for additional watchtowers.
  XOnlyPublicKeys watchtowers = 2;
  /// X-only public keys of operators that will participate in the deposit.
  XOnlyPublicKeys operators = 3;
}

message ReplacementDeposit {
  // Move to vault txid that is being replaced.
  Txid old_move_txid = 1;
}

// A new original deposit request's details.
message BaseDeposit {
  // User's EVM address.
  bytes evm_address = 1;
  // User's recovery taproot address.
  string recovery_taproot_address = 2;
}

enum FeeType {
  UNSPECIFIED = 0;
  CPFP = 1;
  RBF = 2;
  NO_FUNDING = 3;
}

enum NormalTransactionId {
    UNSPECIFIED_TRANSACTION_TYPE = 0;
    ROUND = 1;
    KICKOFF = 2;
    MOVE_TO_VAULT = 3;
    PAYOUT = 4;
    CHALLENGE = 5;
    DISPROVE = 6;
    DISPROVE_TIMEOUT = 7;
    REIMBURSE = 8;
    ALL_NEEDED_FOR_DEPOSIT = 9;
    DUMMY = 10;
    READY_TO_REIMBURSE = 11;
    KICKOFF_NOT_FINALIZED = 12;
    CHALLENGE_TIMEOUT = 13;
    BURN_UNUSED_KICKOFF_CONNECTORS = 14;
    YIELD_KICKOFF_TXID = 15;
    REPLACEMENT_DEPOSIT = 17;
    LATEST_BLOCKHASH_TIMEOUT = 18;
    LATEST_BLOCKHASH = 19;
    OPTIMISTIC_PAYOUT = 20;
}

enum NumberedTransactionType {
    UNSPECIFIED_INDEXED_TRANSACTION_TYPE = 0;
    WATCHTOWER_CHALLENGE = 1;
    OPERATOR_CHALLENGE_NACK = 2;
    OPERATOR_CHALLENGE_ACK = 3;
    ASSERT_TIMEOUT = 4;
    UNSPENT_KICKOFF = 5;
    MINI_ASSERT = 6;
    WATCHTOWER_CHALLENGE_TIMEOUT = 7;
}

message NumberedTransactionId {
  NumberedTransactionType transaction_type = 1;
  int32 index = 2;
}

message GrpcTransactionId {
  oneof id {
    NormalTransactionId normal_transaction = 1;
    NumberedTransactionId numbered_transaction = 2;
  }
}

message KickoffId {
  bytes operator_xonly_pk = 1;
  uint32 round_idx = 2;
  uint32 kickoff_idx = 3;
}

message TransactionRequest {
  Outpoint deposit_outpoint = 1;
  KickoffId kickoff_id = 2;
}

// Includes the deposit params and the nonce gen initial responses (pubkeys and their signatures from all verifiers)
message DepositSignSession {
  DepositParams deposit_params = 1;
  repeated NonceGenFirstResponse nonce_gen_first_responses = 2;
}

// Operator --------------------------------------------------------------------

message OperatorConfig {
  Outpoint collateral_funding_outpoint = 1;
  string xonly_pk = 2;
  string wallet_reimburse_address = 3;
}

message OperatorParams {
  oneof response {
    // Operator's configuration.
    OperatorConfig operator_details = 1;
    // Winternitz pubkeys for each kickoff utxo (to commit blockhash).
    WinternitzPubkey winternitz_pubkeys = 2;
    // unspent kickoff signatures
    SchnorrSig unspent_kickoff_sig = 3;
  }
}

message OperatorKeysWithDeposit {
  OperatorKeys operator_keys = 1;
  DepositParams deposit_params = 2;
  bytes operator_xonly_pk = 3;
}

message OperatorKeys {
  // Winternitz pubkeys for each bitvm assert tx.
  repeated WinternitzPubkey winternitz_pubkeys = 1;
  // Hashes of preimages that will be used to ACK watchtower challenges.
  repeated ChallengeACKDigest challenge_ack_digests = 2;
}

message SchnorrSig {
  bytes schnorr_sig = 1;
}

message WithdrawParams {
  // The ID of the withdrawal in Citrea
  uint32 withdrawal_id = 1;
  // User's [`bitcoin::sighash::TapSighashType::SinglePlusAnyoneCanPay`]
  // signature
  bytes input_signature = 2;
  // User's UTXO to claim the deposit
  Outpoint input_outpoint = 3;
  // The withdrawal output's script_pubkey (user's signature is only valid for this pubkey)
  bytes output_script_pubkey = 4;
  // The withdrawal output's amount (user's signature is only valid for this amount)
  uint64 output_amount = 5;
}


message FinalizedPayoutParams {
  bytes payout_blockhash = 1;
  Outpoint deposit_outpoint = 2;
}

message XOnlyPublicKeyRpc {
  bytes xonly_public_key = 1;
}

message StoppedTasks {
  repeated string stopped_tasks = 1;
}

message EntityError {
  string error = 1;
}

message EntityStatus {
  bool automation = 1;
  optional string wallet_balance = 2;
  optional uint32 tx_sender_synced_height = 3;
  optional uint32 finalized_synced_height = 4;
  optional uint32 hcp_last_proven_height = 5;
  StoppedTasks stopped_tasks = 6;
  optional uint32 rpc_tip_height = 7;
  optional uint32 bitcoin_syncer_synced_height = 8;
  optional uint32 state_manager_next_height = 9;
}

enum EntityType {
  ENTITY_UNKNOWN = 0;
  OPERATOR = 1;
  VERIFIER = 2;
}

message EntityId {
  EntityType kind = 1;
  string id = 2;
}

message EntityStatusWithId {
  EntityId entity_id = 1;
  oneof status_result {
    EntityStatus status = 2;
    EntityError err = 3;
  }
}

message EntityStatuses {
  repeated EntityStatusWithId entity_statuses = 1;
}

// An operator is responsible for paying withdrawals. It has an unique ID and
// chain of UTXOs named `round_txs`. An operator also runs a verifier. These are
// connected to the same database and both have access to watchtowers'
// winternitz pubkeys.
service ClementineOperator {
  // Returns the operator's xonly public key
  //
  // Used by aggregator inside setup
  rpc GetXOnlyPublicKey(Empty) returns (XOnlyPublicKeyRpc) {}

  // Returns an operator's parameters. It will be called once, by the
  // aggregator, to set all the public keys.
  //
  // # Returns
  //
  // Returns an [`OperatorParams`], which includes operator's configuration and
  // Watchtower parameters.
  //
  // Used by aggregator inside setup
  rpc GetParams(Empty) returns (stream OperatorParams) {}

  // Returns an operator's deposit keys.
  // Deposit keys include Assert BitVM winternitz keys, and challenge ACK hashes.
  //
  // Used by aggregator inside new_deposit
  rpc GetDepositKeys(DepositParams) returns (OperatorKeys) {}

  // Returns the current status of tasks running on the operator and their last synced heights.
  rpc GetCurrentStatus(Empty) returns (EntityStatus) {}

  // Signs everything that includes Operator's burn connector.
  //
  // # Parameters
  //
  // - User's deposit information
  // - Nonce metadata
  //
  // # Returns
  //
  // - Operator burn Schnorr signature
  rpc DepositSign(DepositSignSession) returns (stream SchnorrSig) {}

  // Restarts the background tasks for the operator.
  rpc RestartBackgroundTasks(Empty) returns (Empty) {}

  // Prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
  // If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
  // If automation is disabled, the withdrawal will not be accepted and an error will be returned.
  // Note: This is intended for operator's own use, so it doesn't include a signature from aggregator.
  rpc InternalWithdraw(WithdrawParams)
    returns (RawSignedTx) {}


  // First, if verification address in operator's config is set, the signature in rpc is checked to see if it was signed by the verification address.
  // Then prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
  // If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
  // If automation is disabled, the withdrawal will not be accepted and an error will be returned.
  rpc Withdraw(WithdrawParamsWithSig)
    returns (RawSignedTx) {}

  // For a given deposit outpoint, determines the next step in the kickoff process the operator is in,
  // and returns the raw signed txs that the operator needs to send next, for enabling reimbursement process
  // without automation.
  //
  // # Parameters
  // - deposit_outpoint: Deposit outpoint to create the kickoff for
  //
  // # Returns
  // - Raw signed txs that the operator needs to send next
  rpc GetReimbursementTxs(Outpoint) returns (SignedTxsWithType) {}

  // Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
  // Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
  // It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
  //
  // # Parameters
  // - deposit_params: User's deposit information
  // - transaction_type: Requested Transaction type
  // - kickoff_id: Operator's kickoff ID
  //
  // # Returns
  // - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
  //
  // Only used in tests
  rpc InternalCreateSignedTxs(TransactionRequest) returns (SignedTxsWithType) {}

  // Creates all assert transactions (AssertBegin, MiniAsserts, AssertEnd), signs them, and returns the raw txs
  // in the same order.
  // # Parameters
  // - deposit_params: User's deposit information
  // - kickoff_id: Operator's kickoff ID
  // - commit_data: Commitment data for each MiniAssert tx's
  //
  // # Returns
  // - Raw signed assert transactions
  rpc InternalCreateAssertCommitmentTxs(TransactionRequest) returns (SignedTxsWithType) {}

  rpc InternalFinalizedPayout(FinalizedPayoutParams) returns (Txid) {}

  rpc InternalEndRound(Empty) returns (Empty) {}

  rpc Vergen(Empty) returns (VergenResponse) {}
}

// Verifier --------------------------------------------------------------------

message VerifierParams {
  bytes public_key = 1;
}

message PartialSig {
  bytes partial_sig = 1;
}

message NonceGenRequest {
  uint32 num_nonces = 1;
}

message NonceGenFirstResponse {
  // ID of the nonce session (used to store nonces in verifier's memory)
  // The id is string representation of a u128 number
  string id = 1;
  // Number of nonces to generate
  uint32 num_nonces = 2;
}
message NonceGenResponse {
  oneof response {
    NonceGenFirstResponse first_response = 1;
    bytes pub_nonce = 2;
  }
}

message OptimisticWithdrawParams {
  WithdrawParams withdrawal = 1;
  // An ECDSA signature (of citrea/aggregator) over the withdrawal params
  // to authenticate the withdrawal params. This will be signed manually by citrea
  // after manual verification of the optimistic payout.
  optional string verification_signature = 2;
}

message WithdrawParamsWithSig {
  WithdrawParams withdrawal = 1;
  // An ECDSA signature (of citrea/aggregator) over the withdrawal params
  // to authenticate the withdrawal params. This will be signed manually by citrea
  // after manual verification of the optimistic payout.
  // This message contains same data as the one in Optimistic Payout signature, but with a different message name,
  // so that the same signature can't be used for both optimistic payout and normal withdrawal.
  optional string verification_signature = 2;
}

// Input of the aggregator's withdraw function.
// It contains the withdrawal params along with the verification signature that signs the withdrawal params.
// It also contains the operator's xonly public keys that the withdrawal request should be sent to. If the list is empty, the withdrawal will be sent to all operators.
message AggregatorWithdrawalInput {
  WithdrawParamsWithSig withdrawal = 1;
  repeated XOnlyPublicKeyRpc operator_xonly_pks = 2;
}

message OptimisticPayoutParams {
  OptimisticWithdrawParams opt_withdrawal = 1;
  NonceGenFirstResponse nonce_gen = 2;
  bytes agg_nonce = 3;
}

message VerifierDepositSignParams {
  oneof params {
    DepositSignSession deposit_sign_first_param = 1;
    bytes agg_nonce = 2;
  }
}

message VerifierDepositFinalizeParams {
  oneof params {
    DepositSignSession deposit_sign_first_param = 1;
    bytes schnorr_sig = 2;
    bytes move_tx_agg_nonce = 3;
    bytes emergency_stop_agg_nonce = 4;
  }
}

message VerifierDepositFinalizeResponse {
  bytes move_to_vault_partial_sig = 1;
  bytes emergency_stop_partial_sig = 2;
}

message VerifierPublicKeys {
  repeated bytes verifier_public_keys = 1;
}

message TxDebugRequest {
  uint32 tx_id = 1;
}

message TxDebugSubmissionError {
  string error_message = 1;
  string timestamp = 2;
}

message TxDebugFeePayerUtxo {
  Txid txid = 1;
  uint32 vout = 2;
  uint64 amount = 3;
  bool confirmed = 4;
}

message TxMetadata {
  // Optional outpoint of the deposit transaction
  Outpoint deposit_outpoint = 1;
  // Deposit identification
  XOnlyPublicKeyRpc operator_xonly_pk = 2;
  uint32 round_idx = 4;
  uint32 kickoff_idx = 5;
  // Transaction ID
  GrpcTransactionId tx_type = 6;
}

message TxDebugInfo {
  uint32 id = 1;
  bool is_active = 2;
  string current_state = 3;
  repeated TxDebugSubmissionError submission_errors = 4;
  repeated TxDebugFeePayerUtxo fee_payer_utxos = 5;
  string created_at = 6;
  Txid txid = 7;
  string fee_paying_type = 8;
  uint32 fee_payer_utxos_count = 9;
  uint32 fee_payer_utxos_confirmed_count = 10;
  bytes raw_tx = 11;
  TxMetadata metadata = 12;
}

message XOnlyPublicKeys {
  repeated bytes xonly_public_keys = 1;
}


message VergenResponse {
  string response = 1;
}

service ClementineVerifier {
  // Returns verifiers' metadata. Needs to be called once per setup.
  //
  // Used by aggregator inside setup to let all verifiers know all other verifier pks
  rpc GetParams(Empty) returns (VerifierParams) {}

  // Saves an operator.
  //
  // Used by aggregator inside setup to let all verifiers know all other operator pks
  rpc SetOperator(stream OperatorParams) returns (Empty) {}

  // Sets the operator's winternitz keys and challenge ACK hashes and saves them
  // into the db.
  //
  // Used by aggregator inside new_deposit to let all verifiers know all other operators' deposit information
  rpc SetOperatorKeys(OperatorKeysWithDeposit) returns (Empty) {}

  // Generates nonces for a deposit.
  //
  // # Returns
  //
  // Nonce metadata followed by nonces.
  //
  // Used by aggregator inside new_deposit
  rpc NonceGen(NonceGenRequest) returns (stream NonceGenResponse) {}

  // Signs deposit with given aggNonces and verifier's secNonce using
  // nonce_id.
  //
  // Used by aggregator inside new_deposit
  rpc DepositSign(stream VerifierDepositSignParams)
    returns (stream PartialSig) {}

  // Signs the optimistic payout tx with given aggNonce and withdrawal info.
  rpc OptimisticPayoutSign(OptimisticPayoutParams) returns (PartialSig) {}

  // Verifies every signature and signs move_tx.
  //
  // Used by aggregator inside new_deposit
  rpc DepositFinalize(stream VerifierDepositFinalizeParams)
    returns (VerifierDepositFinalizeResponse) {}

  // Debug a transaction by retrieving its current state and history
  rpc DebugTx(TxDebugRequest) returns (TxDebugInfo) {}

  // Restarts the background tasks for the verifier.
  rpc RestartBackgroundTasks(Empty) returns (Empty) {}

  // Checks if the kickoff tx is malicious and if so, try to send all necessary txs to punish the operator
  rpc InternalHandleKickoff(Txid) returns (Empty) {}

  // Returns the current status of tasks running on the verifier and their last synced heights.
  rpc GetCurrentStatus(Empty) returns (EntityStatus) {}

  // 1. Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
  // 2. Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
  // 3. It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
  //
  // # Parameters
  // - deposit_params: User's deposit information
  // - transaction_type: Requested Transaction type
  // - kickoff_id: Operator's kickoff ID
  //
  // # Returns
  // - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
  rpc InternalCreateSignedTxs(TransactionRequest) returns (SignedTxsWithType) {}

  // Signs the verifiers own watchtower challenge tx in the corresponding
  // kickoff and returns the signed raw tx
  rpc InternalCreateWatchtowerChallenge(TransactionRequest) returns (RawTxWithRbfInfo) {}

  rpc Vergen(Empty) returns (VergenResponse) {}
}

// Aggregator ------------------------------------------------------------------

message RawSignedTx {
  bytes raw_tx = 1;
}

message SendTxRequest {
  RawSignedTx raw_tx = 1;
  FeeType fee_type = 2;
}

message RawSignedTxs {
  repeated RawSignedTx raw_txs = 1;
}

message SignedTxWithType {
  GrpcTransactionId transaction_type = 1;
  bytes raw_tx = 2;
}

message SignedTxsWithType {
  repeated SignedTxWithType signed_txs = 1;
}

message RbfSigningInfoRpc {
  bytes merkle_root = 1;
  uint32 vout = 2;
}

message RawTxWithRbfInfo {
  bytes raw_tx = 1;
  RbfSigningInfoRpc rbf_info = 2;
}

message OperatorWithrawalResponse {
  XOnlyPublicKeyRpc operator_xonly_pk = 1;
  oneof response {
    RawSignedTx raw_tx = 2;
    string error = 3;
  }
}

message AggregatorWithdrawResponse {
  repeated OperatorWithrawalResponse withdraw_responses = 1;
}

message GetEmergencyStopTxRequest {
  repeated Txid txids = 1;
}

message GetEmergencyStopTxResponse {
  repeated Txid txids = 1;
  repeated bytes encrypted_emergency_stop_txs = 2;
}

message SendMoveTxRequest {
  RawSignedTx raw_tx = 1;
  Outpoint deposit_outpoint = 2;
}

message GetEntityStatusesRequest {
  bool restart_tasks = 1;
}

service ClementineAggregator {
  rpc GetNofnAggregatedXonlyPk(Empty) returns (NofnResponse) {}

  // Sets up the system of verifiers, watchtowers and operators by:
  //
  // 1. Collects verifier keys from each verifier
  // 2. Distributes these verifier keys to all verifiers
  // 3. Collects all operator configs from each operator
  // 4. Distributes these operator configs to all verifiers
  //
  // Used by the clementine-backend service
  rpc Setup(Empty) returns (VerifierPublicKeys) {}

  // This will call, DepositNonceGen for every verifier,
  // then it will aggregate one by one and then send it to DepositSign,
  // then it will aggregate the partial sigs and send it to DepositFinalize,
  // this will also call the operator to get their signatures and send it to
  // DepositFinalize then it will collect the partial sigs and create the move
  // tx.
  //
  // Used by the clementine-backend service to initiate a deposit
  rpc NewDeposit(Deposit) returns (RawSignedTx) {}

  // Call's withdraw on all operators
  // Used by the clementine-backend service to initiate a withdrawal
  // If the operator's xonly public keys list is empty, the withdrawal will be sent to all operators.
  // If not, only the operators in the list will be sent the withdrawal request.
  rpc Withdraw(AggregatorWithdrawalInput)
    returns (AggregatorWithdrawResponse) {}

  // Perform an optimistic payout to reimburse a peg-out from Citrea
  rpc OptimisticPayout(OptimisticWithdrawParams) returns (RawSignedTx)  {}

  // Send a pre-signed tx to the network
  rpc InternalSendTx(SendTxRequest) returns (Empty) {}

  rpc SendMoveToVaultTx(SendMoveTxRequest) returns (Txid) {}

  // Returns the current status of tasks running on the operators/verifiers.
  // If restart_tasks is true, it will restart the tasks on the entities if they are stopped.
  rpc GetEntityStatuses(GetEntityStatusesRequest) returns (EntityStatuses) {}

  // Creates an emergency stop tx that won't be broadcasted.
  // Tx will have around 3 sats/vbyte fee.
  // Set add_anchor to true to add an anchor output for cpfp..
  rpc InternalGetEmergencyStopTx(GetEmergencyStopTxRequest) returns (GetEmergencyStopTxResponse) {}

  rpc Vergen(Empty) returns (VergenResponse) {}
}
