// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct Empty {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Txid {
    #[prost(bytes = "vec", tag = "1")]
    pub txid: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Outpoint {
    #[prost(message, optional, tag = "1")]
    pub txid: ::core::option::Option<Txid>,
    #[prost(uint32, tag = "2")]
    pub vout: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NofnResponse {
    #[prost(bytes = "vec", tag = "1")]
    pub nofn_xonly_pk: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint32, tag = "2")]
    pub num_verifiers: u32,
}
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct NormalSignatureId {
    #[prost(enumeration = "NormalSignatureKind", tag = "1")]
    pub signature_kind: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct NumberedSignatureId {
    #[prost(enumeration = "NumberedSignatureKind", tag = "1")]
    pub signature_kind: i32,
    #[prost(int32, tag = "2")]
    pub idx: i32,
}
/// A tagged signature struct that identifies the transaction-input that the signature is for.
/// The id is left as NotStored for signatures that are created on the fly by the operator (they're also not stored).
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TaggedSignature {
    #[prost(bytes = "vec", tag = "3")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(oneof = "tagged_signature::SignatureId", tags = "1, 2")]
    pub signature_id: ::core::option::Option<tagged_signature::SignatureId>,
}
/// Nested message and enum types in `TaggedSignature`.
pub mod tagged_signature {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum SignatureId {
        #[prost(message, tag = "1")]
        NormalSignature(super::NormalSignatureId),
        #[prost(message, tag = "2")]
        NumberedSignature(super::NumberedSignatureId),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DepositSignatures {
    #[prost(message, repeated, tag = "1")]
    pub signatures: ::prost::alloc::vec::Vec<TaggedSignature>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ChallengeAckDigest {
    #[prost(bytes = "vec", tag = "1")]
    pub hash: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WinternitzPubkey {
    #[prost(bytes = "vec", repeated, tag = "3")]
    pub digit_pubkey: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DepositParams {
    #[prost(message, optional, tag = "1")]
    pub deposit: ::core::option::Option<Deposit>,
    #[prost(message, optional, tag = "2")]
    pub actors: ::core::option::Option<Actors>,
    #[prost(message, optional, tag = "3")]
    pub security_council: ::core::option::Option<SecurityCouncil>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SecurityCouncil {
    #[prost(bytes = "vec", repeated, tag = "1")]
    pub pks: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
    #[prost(uint32, tag = "2")]
    pub threshold: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Deposit {
    /// / User's deposit UTXO.
    #[prost(message, optional, tag = "1")]
    pub deposit_outpoint: ::core::option::Option<Outpoint>,
    #[prost(oneof = "deposit::DepositData", tags = "2, 3")]
    pub deposit_data: ::core::option::Option<deposit::DepositData>,
}
/// Nested message and enum types in `Deposit`.
pub mod deposit {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum DepositData {
        #[prost(message, tag = "2")]
        BaseDeposit(super::BaseDeposit),
        #[prost(message, tag = "3")]
        ReplacementDeposit(super::ReplacementDeposit),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Actors {
    /// / Public keys of verifiers that will participate in the deposit.
    #[prost(message, optional, tag = "1")]
    pub verifiers: ::core::option::Option<VerifierPublicKeys>,
    /// / X-only public keys of watchtowers that will participate in the deposit.
    /// / NOTE: verifiers are automatically considered watchtowers. This field is only for additional watchtowers.
    #[prost(message, optional, tag = "2")]
    pub watchtowers: ::core::option::Option<XOnlyPublicKeys>,
    /// / X-only public keys of operators that will participate in the deposit.
    #[prost(message, optional, tag = "3")]
    pub operators: ::core::option::Option<XOnlyPublicKeys>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ReplacementDeposit {
    /// Move to vault txid that is being replaced.
    #[prost(message, optional, tag = "1")]
    pub old_move_txid: ::core::option::Option<Txid>,
}
/// A new original deposit request's details.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct BaseDeposit {
    /// User's EVM address.
    #[prost(bytes = "vec", tag = "1")]
    pub evm_address: ::prost::alloc::vec::Vec<u8>,
    /// User's recovery taproot address.
    #[prost(string, tag = "2")]
    pub recovery_taproot_address: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct NumberedTransactionId {
    #[prost(enumeration = "NumberedTransactionType", tag = "1")]
    pub transaction_type: i32,
    #[prost(int32, tag = "2")]
    pub index: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GrpcTransactionId {
    #[prost(oneof = "grpc_transaction_id::Id", tags = "1, 2")]
    pub id: ::core::option::Option<grpc_transaction_id::Id>,
}
/// Nested message and enum types in `GrpcTransactionId`.
pub mod grpc_transaction_id {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Id {
        #[prost(enumeration = "super::NormalTransactionId", tag = "1")]
        NormalTransaction(i32),
        #[prost(message, tag = "2")]
        NumberedTransaction(super::NumberedTransactionId),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct KickoffId {
    #[prost(bytes = "vec", tag = "1")]
    pub operator_xonly_pk: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint32, tag = "2")]
    pub round_idx: u32,
    #[prost(uint32, tag = "3")]
    pub kickoff_idx: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TransactionRequest {
    #[prost(message, optional, tag = "1")]
    pub deposit_outpoint: ::core::option::Option<Outpoint>,
    #[prost(message, optional, tag = "2")]
    pub kickoff_id: ::core::option::Option<KickoffId>,
}
/// Includes the deposit params and the nonce gen initial responses (pubkeys and their signatures from all verifiers)
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DepositSignSession {
    #[prost(message, optional, tag = "1")]
    pub deposit_params: ::core::option::Option<DepositParams>,
    #[prost(message, repeated, tag = "2")]
    pub nonce_gen_first_responses: ::prost::alloc::vec::Vec<NonceGenFirstResponse>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OperatorConfig {
    #[prost(message, optional, tag = "1")]
    pub collateral_funding_outpoint: ::core::option::Option<Outpoint>,
    #[prost(string, tag = "2")]
    pub xonly_pk: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub wallet_reimburse_address: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OperatorParams {
    #[prost(oneof = "operator_params::Response", tags = "1, 2, 3")]
    pub response: ::core::option::Option<operator_params::Response>,
}
/// Nested message and enum types in `OperatorParams`.
pub mod operator_params {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Response {
        /// Operator's configuration.
        #[prost(message, tag = "1")]
        OperatorDetails(super::OperatorConfig),
        /// Winternitz pubkeys for each kickoff utxo (to commit blockhash).
        #[prost(message, tag = "2")]
        WinternitzPubkeys(super::WinternitzPubkey),
        /// unspent kickoff signatures
        #[prost(message, tag = "3")]
        UnspentKickoffSig(super::SchnorrSig),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OperatorKeysWithDeposit {
    #[prost(message, optional, tag = "1")]
    pub operator_keys: ::core::option::Option<OperatorKeys>,
    #[prost(message, optional, tag = "2")]
    pub deposit_params: ::core::option::Option<DepositParams>,
    #[prost(bytes = "vec", tag = "3")]
    pub operator_xonly_pk: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OperatorKeys {
    /// Winternitz pubkeys for each bitvm assert tx.
    #[prost(message, repeated, tag = "1")]
    pub winternitz_pubkeys: ::prost::alloc::vec::Vec<WinternitzPubkey>,
    /// Hashes of preimages that will be used to ACK watchtower challenges.
    #[prost(message, repeated, tag = "2")]
    pub challenge_ack_digests: ::prost::alloc::vec::Vec<ChallengeAckDigest>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SchnorrSig {
    #[prost(bytes = "vec", tag = "1")]
    pub schnorr_sig: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WithdrawParams {
    /// The ID of the withdrawal in Citrea
    #[prost(uint32, tag = "1")]
    pub withdrawal_id: u32,
    /// User's \[`bitcoin::sighash::TapSighashType::SinglePlusAnyoneCanPay`\]
    /// signature
    #[prost(bytes = "vec", tag = "2")]
    pub input_signature: ::prost::alloc::vec::Vec<u8>,
    /// User's UTXO to claim the deposit
    #[prost(message, optional, tag = "3")]
    pub input_outpoint: ::core::option::Option<Outpoint>,
    /// The withdrawal output's script_pubkey (user's signature is only valid for this pubkey)
    #[prost(bytes = "vec", tag = "4")]
    pub output_script_pubkey: ::prost::alloc::vec::Vec<u8>,
    /// The withdrawal output's amount (user's signature is only valid for this amount)
    #[prost(uint64, tag = "5")]
    pub output_amount: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FinalizedPayoutParams {
    #[prost(bytes = "vec", tag = "1")]
    pub payout_blockhash: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "2")]
    pub deposit_outpoint: ::core::option::Option<Outpoint>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct XOnlyPublicKeyRpc {
    #[prost(bytes = "vec", tag = "1")]
    pub xonly_public_key: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoppedTasks {
    #[prost(string, repeated, tag = "1")]
    pub stopped_tasks: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EntityError {
    #[prost(string, tag = "1")]
    pub error: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EntityStatus {
    #[prost(bool, tag = "1")]
    pub automation: bool,
    #[prost(string, optional, tag = "2")]
    pub wallet_balance: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(uint32, optional, tag = "3")]
    pub tx_sender_synced_height: ::core::option::Option<u32>,
    #[prost(uint32, optional, tag = "4")]
    pub finalized_synced_height: ::core::option::Option<u32>,
    #[prost(uint32, optional, tag = "5")]
    pub hcp_last_proven_height: ::core::option::Option<u32>,
    #[prost(message, optional, tag = "6")]
    pub stopped_tasks: ::core::option::Option<StoppedTasks>,
    #[prost(uint32, optional, tag = "7")]
    pub rpc_tip_height: ::core::option::Option<u32>,
    #[prost(uint32, optional, tag = "8")]
    pub bitcoin_syncer_synced_height: ::core::option::Option<u32>,
    #[prost(uint32, optional, tag = "9")]
    pub state_manager_next_height: ::core::option::Option<u32>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EntityId {
    #[prost(enumeration = "EntityType", tag = "1")]
    pub kind: i32,
    #[prost(string, tag = "2")]
    pub id: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EntityStatusWithId {
    #[prost(message, optional, tag = "1")]
    pub entity_id: ::core::option::Option<EntityId>,
    #[prost(oneof = "entity_status_with_id::StatusResult", tags = "2, 3")]
    pub status_result: ::core::option::Option<entity_status_with_id::StatusResult>,
}
/// Nested message and enum types in `EntityStatusWithId`.
pub mod entity_status_with_id {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum StatusResult {
        #[prost(message, tag = "2")]
        Status(super::EntityStatus),
        #[prost(message, tag = "3")]
        Err(super::EntityError),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EntityStatuses {
    #[prost(message, repeated, tag = "1")]
    pub entity_statuses: ::prost::alloc::vec::Vec<EntityStatusWithId>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VerifierParams {
    #[prost(bytes = "vec", tag = "1")]
    pub public_key: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PartialSig {
    #[prost(bytes = "vec", tag = "1")]
    pub partial_sig: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct NonceGenRequest {
    #[prost(uint32, tag = "1")]
    pub num_nonces: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NonceGenFirstResponse {
    /// ID of the nonce session (used to store nonces in verifier's memory)
    /// The id is string representation of a u128 number
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    /// Number of nonces to generate
    #[prost(uint32, tag = "2")]
    pub num_nonces: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct NonceGenResponse {
    #[prost(oneof = "nonce_gen_response::Response", tags = "1, 2")]
    pub response: ::core::option::Option<nonce_gen_response::Response>,
}
/// Nested message and enum types in `NonceGenResponse`.
pub mod nonce_gen_response {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Response {
        #[prost(message, tag = "1")]
        FirstResponse(super::NonceGenFirstResponse),
        #[prost(bytes, tag = "2")]
        PubNonce(::prost::alloc::vec::Vec<u8>),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OptimisticWithdrawParams {
    #[prost(message, optional, tag = "1")]
    pub withdrawal: ::core::option::Option<WithdrawParams>,
    /// An ECDSA signature (of citrea/aggregator) over the withdrawal params
    /// to authenticate the withdrawal params. This will be signed manually by citrea
    /// after manual verification of the optimistic payout.
    #[prost(string, optional, tag = "2")]
    pub verification_signature: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WithdrawParamsWithSig {
    #[prost(message, optional, tag = "1")]
    pub withdrawal: ::core::option::Option<WithdrawParams>,
    /// An ECDSA signature (of citrea/aggregator) over the withdrawal params
    /// to authenticate the withdrawal params. This will be signed manually by citrea
    /// after manual verification of the optimistic payout.
    /// This message contains same data as the one in Optimistic Payout signature, but with a different message name,
    /// so that the same signature can't be used for both optimistic payout and normal withdrawal.
    #[prost(string, optional, tag = "2")]
    pub verification_signature: ::core::option::Option<::prost::alloc::string::String>,
}
/// Input of the aggregator's withdraw function.
/// It contains the withdrawal params along with the verification signature that signs the withdrawal params.
/// It also contains the operator's xonly public keys that the withdrawal request should be sent to. If the list is empty, the withdrawal will be sent to all operators.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AggregatorWithdrawalInput {
    #[prost(message, optional, tag = "1")]
    pub withdrawal: ::core::option::Option<WithdrawParamsWithSig>,
    #[prost(message, repeated, tag = "2")]
    pub operator_xonly_pks: ::prost::alloc::vec::Vec<XOnlyPublicKeyRpc>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OptimisticPayoutParams {
    #[prost(message, optional, tag = "1")]
    pub opt_withdrawal: ::core::option::Option<OptimisticWithdrawParams>,
    #[prost(message, optional, tag = "2")]
    pub nonce_gen: ::core::option::Option<NonceGenFirstResponse>,
    #[prost(bytes = "vec", tag = "3")]
    pub agg_nonce: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VerifierDepositSignParams {
    #[prost(oneof = "verifier_deposit_sign_params::Params", tags = "1, 2")]
    pub params: ::core::option::Option<verifier_deposit_sign_params::Params>,
}
/// Nested message and enum types in `VerifierDepositSignParams`.
pub mod verifier_deposit_sign_params {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Params {
        #[prost(message, tag = "1")]
        DepositSignFirstParam(super::DepositSignSession),
        #[prost(bytes, tag = "2")]
        AggNonce(::prost::alloc::vec::Vec<u8>),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VerifierDepositFinalizeParams {
    #[prost(oneof = "verifier_deposit_finalize_params::Params", tags = "1, 2, 3, 4")]
    pub params: ::core::option::Option<verifier_deposit_finalize_params::Params>,
}
/// Nested message and enum types in `VerifierDepositFinalizeParams`.
pub mod verifier_deposit_finalize_params {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Params {
        #[prost(message, tag = "1")]
        DepositSignFirstParam(super::DepositSignSession),
        #[prost(bytes, tag = "2")]
        SchnorrSig(::prost::alloc::vec::Vec<u8>),
        #[prost(bytes, tag = "3")]
        MoveTxAggNonce(::prost::alloc::vec::Vec<u8>),
        #[prost(bytes, tag = "4")]
        EmergencyStopAggNonce(::prost::alloc::vec::Vec<u8>),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VerifierDepositFinalizeResponse {
    #[prost(bytes = "vec", tag = "1")]
    pub move_to_vault_partial_sig: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub emergency_stop_partial_sig: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VerifierPublicKeys {
    #[prost(bytes = "vec", repeated, tag = "1")]
    pub verifier_public_keys: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct TxDebugRequest {
    #[prost(uint32, tag = "1")]
    pub tx_id: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TxDebugSubmissionError {
    #[prost(string, tag = "1")]
    pub error_message: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub timestamp: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TxDebugFeePayerUtxo {
    #[prost(message, optional, tag = "1")]
    pub txid: ::core::option::Option<Txid>,
    #[prost(uint32, tag = "2")]
    pub vout: u32,
    #[prost(uint64, tag = "3")]
    pub amount: u64,
    #[prost(bool, tag = "4")]
    pub confirmed: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TxMetadata {
    /// Optional outpoint of the deposit transaction
    #[prost(message, optional, tag = "1")]
    pub deposit_outpoint: ::core::option::Option<Outpoint>,
    /// Deposit identification
    #[prost(message, optional, tag = "2")]
    pub operator_xonly_pk: ::core::option::Option<XOnlyPublicKeyRpc>,
    #[prost(uint32, tag = "4")]
    pub round_idx: u32,
    #[prost(uint32, tag = "5")]
    pub kickoff_idx: u32,
    /// Transaction ID
    #[prost(message, optional, tag = "6")]
    pub tx_type: ::core::option::Option<GrpcTransactionId>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TxDebugInfo {
    #[prost(uint32, tag = "1")]
    pub id: u32,
    #[prost(bool, tag = "2")]
    pub is_active: bool,
    #[prost(string, tag = "3")]
    pub current_state: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "4")]
    pub submission_errors: ::prost::alloc::vec::Vec<TxDebugSubmissionError>,
    #[prost(message, repeated, tag = "5")]
    pub fee_payer_utxos: ::prost::alloc::vec::Vec<TxDebugFeePayerUtxo>,
    #[prost(string, tag = "6")]
    pub created_at: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "7")]
    pub txid: ::core::option::Option<Txid>,
    #[prost(string, tag = "8")]
    pub fee_paying_type: ::prost::alloc::string::String,
    #[prost(uint32, tag = "9")]
    pub fee_payer_utxos_count: u32,
    #[prost(uint32, tag = "10")]
    pub fee_payer_utxos_confirmed_count: u32,
    #[prost(bytes = "vec", tag = "11")]
    pub raw_tx: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "12")]
    pub metadata: ::core::option::Option<TxMetadata>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct XOnlyPublicKeys {
    #[prost(bytes = "vec", repeated, tag = "1")]
    pub xonly_public_keys: ::prost::alloc::vec::Vec<::prost::alloc::vec::Vec<u8>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VergenResponse {
    #[prost(string, tag = "1")]
    pub response: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RawSignedTx {
    #[prost(bytes = "vec", tag = "1")]
    pub raw_tx: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendTxRequest {
    #[prost(message, optional, tag = "1")]
    pub raw_tx: ::core::option::Option<RawSignedTx>,
    #[prost(enumeration = "FeeType", tag = "2")]
    pub fee_type: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RawSignedTxs {
    #[prost(message, repeated, tag = "1")]
    pub raw_txs: ::prost::alloc::vec::Vec<RawSignedTx>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SignedTxWithType {
    #[prost(message, optional, tag = "1")]
    pub transaction_type: ::core::option::Option<GrpcTransactionId>,
    #[prost(bytes = "vec", tag = "2")]
    pub raw_tx: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SignedTxsWithType {
    #[prost(message, repeated, tag = "1")]
    pub signed_txs: ::prost::alloc::vec::Vec<SignedTxWithType>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RbfSigningInfoRpc {
    #[prost(bytes = "vec", tag = "1")]
    pub merkle_root: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint32, tag = "2")]
    pub vout: u32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RawTxWithRbfInfo {
    #[prost(bytes = "vec", tag = "1")]
    pub raw_tx: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "2")]
    pub rbf_info: ::core::option::Option<RbfSigningInfoRpc>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct OperatorWithrawalResponse {
    #[prost(message, optional, tag = "1")]
    pub operator_xonly_pk: ::core::option::Option<XOnlyPublicKeyRpc>,
    #[prost(oneof = "operator_withrawal_response::Response", tags = "2, 3")]
    pub response: ::core::option::Option<operator_withrawal_response::Response>,
}
/// Nested message and enum types in `OperatorWithrawalResponse`.
pub mod operator_withrawal_response {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Response {
        #[prost(message, tag = "2")]
        RawTx(super::RawSignedTx),
        #[prost(string, tag = "3")]
        Error(::prost::alloc::string::String),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AggregatorWithdrawResponse {
    #[prost(message, repeated, tag = "1")]
    pub withdraw_responses: ::prost::alloc::vec::Vec<OperatorWithrawalResponse>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetEmergencyStopTxRequest {
    #[prost(message, repeated, tag = "1")]
    pub txids: ::prost::alloc::vec::Vec<Txid>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetEmergencyStopTxResponse {
    #[prost(message, repeated, tag = "1")]
    pub txids: ::prost::alloc::vec::Vec<Txid>,
    #[prost(bytes = "vec", repeated, tag = "2")]
    pub encrypted_emergency_stop_txs: ::prost::alloc::vec::Vec<
        ::prost::alloc::vec::Vec<u8>,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendMoveTxRequest {
    #[prost(message, optional, tag = "1")]
    pub raw_tx: ::core::option::Option<RawSignedTx>,
    #[prost(message, optional, tag = "2")]
    pub deposit_outpoint: ::core::option::Option<Outpoint>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetEntityStatusesRequest {
    #[prost(bool, tag = "1")]
    pub restart_tasks: bool,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum NormalSignatureKind {
    NormalSignatureUnknown = 0,
    /// Used for TxHandlers that verifiers don't care. These will have signatures created
    /// by the operator on the fly.
    OperatorSighashDefault = 1,
    Challenge = 2,
    DisproveTimeout2 = 3,
    Disprove2 = 4,
    Reimburse1 = 5,
    KickoffNotFinalized1 = 6,
    KickoffNotFinalized2 = 7,
    Reimburse2 = 8,
    NoSignature = 9,
    ChallengeTimeout2 = 10,
    MiniAssert1 = 11,
    OperatorChallengeAck1 = 12,
    NotStored = 13,
    YieldKickoffTxid = 14,
    LatestBlockhashTimeout1 = 15,
    LatestBlockhashTimeout2 = 16,
    LatestBlockhashTimeout3 = 17,
    LatestBlockhash = 18,
}
impl NormalSignatureKind {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::NormalSignatureUnknown => "NormalSignatureUnknown",
            Self::OperatorSighashDefault => "OperatorSighashDefault",
            Self::Challenge => "Challenge",
            Self::DisproveTimeout2 => "DisproveTimeout2",
            Self::Disprove2 => "Disprove2",
            Self::Reimburse1 => "Reimburse1",
            Self::KickoffNotFinalized1 => "KickoffNotFinalized1",
            Self::KickoffNotFinalized2 => "KickoffNotFinalized2",
            Self::Reimburse2 => "Reimburse2",
            Self::NoSignature => "NoSignature",
            Self::ChallengeTimeout2 => "ChallengeTimeout2",
            Self::MiniAssert1 => "MiniAssert1",
            Self::OperatorChallengeAck1 => "OperatorChallengeAck1",
            Self::NotStored => "NotStored",
            Self::YieldKickoffTxid => "YieldKickoffTxid",
            Self::LatestBlockhashTimeout1 => "LatestBlockhashTimeout1",
            Self::LatestBlockhashTimeout2 => "LatestBlockhashTimeout2",
            Self::LatestBlockhashTimeout3 => "LatestBlockhashTimeout3",
            Self::LatestBlockhash => "LatestBlockhash",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "NormalSignatureUnknown" => Some(Self::NormalSignatureUnknown),
            "OperatorSighashDefault" => Some(Self::OperatorSighashDefault),
            "Challenge" => Some(Self::Challenge),
            "DisproveTimeout2" => Some(Self::DisproveTimeout2),
            "Disprove2" => Some(Self::Disprove2),
            "Reimburse1" => Some(Self::Reimburse1),
            "KickoffNotFinalized1" => Some(Self::KickoffNotFinalized1),
            "KickoffNotFinalized2" => Some(Self::KickoffNotFinalized2),
            "Reimburse2" => Some(Self::Reimburse2),
            "NoSignature" => Some(Self::NoSignature),
            "ChallengeTimeout2" => Some(Self::ChallengeTimeout2),
            "MiniAssert1" => Some(Self::MiniAssert1),
            "OperatorChallengeAck1" => Some(Self::OperatorChallengeAck1),
            "NotStored" => Some(Self::NotStored),
            "YieldKickoffTxid" => Some(Self::YieldKickoffTxid),
            "LatestBlockhashTimeout1" => Some(Self::LatestBlockhashTimeout1),
            "LatestBlockhashTimeout2" => Some(Self::LatestBlockhashTimeout2),
            "LatestBlockhashTimeout3" => Some(Self::LatestBlockhashTimeout3),
            "LatestBlockhash" => Some(Self::LatestBlockhash),
            _ => None,
        }
    }
}
/// Signatures that are needed multiple times per an operators kickoff.
/// Some watchtower sigs are needed once per watchtower.
/// Asserts are needed multiple times
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum NumberedSignatureKind {
    NumberedSignatureUnknown = 0,
    /// Used for TxHandlers that verifiers don't care. These will have signatures created
    /// by the operator on the fly.
    NumberedNotStored = 1,
    OperatorChallengeNack1 = 2,
    OperatorChallengeNack2 = 3,
    OperatorChallengeNack3 = 4,
    AssertTimeout1 = 5,
    AssertTimeout2 = 6,
    AssertTimeout3 = 7,
    UnspentKickoff1 = 8,
    UnspentKickoff2 = 9,
    WatchtowerChallengeTimeout1 = 10,
    WatchtowerChallengeTimeout2 = 11,
    WatchtowerChallenge = 12,
}
impl NumberedSignatureKind {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::NumberedSignatureUnknown => "NumberedSignatureUnknown",
            Self::NumberedNotStored => "NumberedNotStored",
            Self::OperatorChallengeNack1 => "OperatorChallengeNack1",
            Self::OperatorChallengeNack2 => "OperatorChallengeNack2",
            Self::OperatorChallengeNack3 => "OperatorChallengeNack3",
            Self::AssertTimeout1 => "AssertTimeout1",
            Self::AssertTimeout2 => "AssertTimeout2",
            Self::AssertTimeout3 => "AssertTimeout3",
            Self::UnspentKickoff1 => "UnspentKickoff1",
            Self::UnspentKickoff2 => "UnspentKickoff2",
            Self::WatchtowerChallengeTimeout1 => "WatchtowerChallengeTimeout1",
            Self::WatchtowerChallengeTimeout2 => "WatchtowerChallengeTimeout2",
            Self::WatchtowerChallenge => "WatchtowerChallenge",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "NumberedSignatureUnknown" => Some(Self::NumberedSignatureUnknown),
            "NumberedNotStored" => Some(Self::NumberedNotStored),
            "OperatorChallengeNack1" => Some(Self::OperatorChallengeNack1),
            "OperatorChallengeNack2" => Some(Self::OperatorChallengeNack2),
            "OperatorChallengeNack3" => Some(Self::OperatorChallengeNack3),
            "AssertTimeout1" => Some(Self::AssertTimeout1),
            "AssertTimeout2" => Some(Self::AssertTimeout2),
            "AssertTimeout3" => Some(Self::AssertTimeout3),
            "UnspentKickoff1" => Some(Self::UnspentKickoff1),
            "UnspentKickoff2" => Some(Self::UnspentKickoff2),
            "WatchtowerChallengeTimeout1" => Some(Self::WatchtowerChallengeTimeout1),
            "WatchtowerChallengeTimeout2" => Some(Self::WatchtowerChallengeTimeout2),
            "WatchtowerChallenge" => Some(Self::WatchtowerChallenge),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum FeeType {
    Unspecified = 0,
    Cpfp = 1,
    Rbf = 2,
    NoFunding = 3,
}
impl FeeType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Unspecified => "UNSPECIFIED",
            Self::Cpfp => "CPFP",
            Self::Rbf => "RBF",
            Self::NoFunding => "NO_FUNDING",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNSPECIFIED" => Some(Self::Unspecified),
            "CPFP" => Some(Self::Cpfp),
            "RBF" => Some(Self::Rbf),
            "NO_FUNDING" => Some(Self::NoFunding),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum NormalTransactionId {
    UnspecifiedTransactionType = 0,
    Round = 1,
    Kickoff = 2,
    MoveToVault = 3,
    Payout = 4,
    Challenge = 5,
    Disprove = 6,
    DisproveTimeout = 7,
    Reimburse = 8,
    AllNeededForDeposit = 9,
    Dummy = 10,
    ReadyToReimburse = 11,
    KickoffNotFinalized = 12,
    ChallengeTimeout = 13,
    BurnUnusedKickoffConnectors = 14,
    YieldKickoffTxid = 15,
    ReplacementDeposit = 17,
    LatestBlockhashTimeout = 18,
    LatestBlockhash = 19,
    OptimisticPayout = 20,
}
impl NormalTransactionId {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::UnspecifiedTransactionType => "UNSPECIFIED_TRANSACTION_TYPE",
            Self::Round => "ROUND",
            Self::Kickoff => "KICKOFF",
            Self::MoveToVault => "MOVE_TO_VAULT",
            Self::Payout => "PAYOUT",
            Self::Challenge => "CHALLENGE",
            Self::Disprove => "DISPROVE",
            Self::DisproveTimeout => "DISPROVE_TIMEOUT",
            Self::Reimburse => "REIMBURSE",
            Self::AllNeededForDeposit => "ALL_NEEDED_FOR_DEPOSIT",
            Self::Dummy => "DUMMY",
            Self::ReadyToReimburse => "READY_TO_REIMBURSE",
            Self::KickoffNotFinalized => "KICKOFF_NOT_FINALIZED",
            Self::ChallengeTimeout => "CHALLENGE_TIMEOUT",
            Self::BurnUnusedKickoffConnectors => "BURN_UNUSED_KICKOFF_CONNECTORS",
            Self::YieldKickoffTxid => "YIELD_KICKOFF_TXID",
            Self::ReplacementDeposit => "REPLACEMENT_DEPOSIT",
            Self::LatestBlockhashTimeout => "LATEST_BLOCKHASH_TIMEOUT",
            Self::LatestBlockhash => "LATEST_BLOCKHASH",
            Self::OptimisticPayout => "OPTIMISTIC_PAYOUT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNSPECIFIED_TRANSACTION_TYPE" => Some(Self::UnspecifiedTransactionType),
            "ROUND" => Some(Self::Round),
            "KICKOFF" => Some(Self::Kickoff),
            "MOVE_TO_VAULT" => Some(Self::MoveToVault),
            "PAYOUT" => Some(Self::Payout),
            "CHALLENGE" => Some(Self::Challenge),
            "DISPROVE" => Some(Self::Disprove),
            "DISPROVE_TIMEOUT" => Some(Self::DisproveTimeout),
            "REIMBURSE" => Some(Self::Reimburse),
            "ALL_NEEDED_FOR_DEPOSIT" => Some(Self::AllNeededForDeposit),
            "DUMMY" => Some(Self::Dummy),
            "READY_TO_REIMBURSE" => Some(Self::ReadyToReimburse),
            "KICKOFF_NOT_FINALIZED" => Some(Self::KickoffNotFinalized),
            "CHALLENGE_TIMEOUT" => Some(Self::ChallengeTimeout),
            "BURN_UNUSED_KICKOFF_CONNECTORS" => Some(Self::BurnUnusedKickoffConnectors),
            "YIELD_KICKOFF_TXID" => Some(Self::YieldKickoffTxid),
            "REPLACEMENT_DEPOSIT" => Some(Self::ReplacementDeposit),
            "LATEST_BLOCKHASH_TIMEOUT" => Some(Self::LatestBlockhashTimeout),
            "LATEST_BLOCKHASH" => Some(Self::LatestBlockhash),
            "OPTIMISTIC_PAYOUT" => Some(Self::OptimisticPayout),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum NumberedTransactionType {
    UnspecifiedIndexedTransactionType = 0,
    WatchtowerChallenge = 1,
    OperatorChallengeNack = 2,
    OperatorChallengeAck = 3,
    AssertTimeout = 4,
    UnspentKickoff = 5,
    MiniAssert = 6,
    WatchtowerChallengeTimeout = 7,
}
impl NumberedTransactionType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::UnspecifiedIndexedTransactionType => {
                "UNSPECIFIED_INDEXED_TRANSACTION_TYPE"
            }
            Self::WatchtowerChallenge => "WATCHTOWER_CHALLENGE",
            Self::OperatorChallengeNack => "OPERATOR_CHALLENGE_NACK",
            Self::OperatorChallengeAck => "OPERATOR_CHALLENGE_ACK",
            Self::AssertTimeout => "ASSERT_TIMEOUT",
            Self::UnspentKickoff => "UNSPENT_KICKOFF",
            Self::MiniAssert => "MINI_ASSERT",
            Self::WatchtowerChallengeTimeout => "WATCHTOWER_CHALLENGE_TIMEOUT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNSPECIFIED_INDEXED_TRANSACTION_TYPE" => {
                Some(Self::UnspecifiedIndexedTransactionType)
            }
            "WATCHTOWER_CHALLENGE" => Some(Self::WatchtowerChallenge),
            "OPERATOR_CHALLENGE_NACK" => Some(Self::OperatorChallengeNack),
            "OPERATOR_CHALLENGE_ACK" => Some(Self::OperatorChallengeAck),
            "ASSERT_TIMEOUT" => Some(Self::AssertTimeout),
            "UNSPENT_KICKOFF" => Some(Self::UnspentKickoff),
            "MINI_ASSERT" => Some(Self::MiniAssert),
            "WATCHTOWER_CHALLENGE_TIMEOUT" => Some(Self::WatchtowerChallengeTimeout),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EntityType {
    EntityUnknown = 0,
    Operator = 1,
    Verifier = 2,
}
impl EntityType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::EntityUnknown => "ENTITY_UNKNOWN",
            Self::Operator => "OPERATOR",
            Self::Verifier => "VERIFIER",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "ENTITY_UNKNOWN" => Some(Self::EntityUnknown),
            "OPERATOR" => Some(Self::Operator),
            "VERIFIER" => Some(Self::Verifier),
            _ => None,
        }
    }
}
/// Generated client implementations.
pub mod clementine_operator_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    /// An operator is responsible for paying withdrawals. It has an unique ID and
    /// chain of UTXOs named `round_txs`. An operator also runs a verifier. These are
    /// connected to the same database and both have access to watchtowers'
    /// winternitz pubkeys.
    #[derive(Debug, Clone)]
    pub struct ClementineOperatorClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ClementineOperatorClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ClementineOperatorClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ClementineOperatorClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ClementineOperatorClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        /// Returns the operator's xonly public key
        ///
        /// Used by aggregator inside setup
        pub async fn get_x_only_public_key(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<
            tonic::Response<super::XOnlyPublicKeyRpc>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/GetXOnlyPublicKey",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineOperator", "GetXOnlyPublicKey"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns an operator's parameters. It will be called once, by the
        /// aggregator, to set all the public keys.
        ///
        /// # Returns
        ///
        /// Returns an [`OperatorParams`], which includes operator's configuration and
        /// Watchtower parameters.
        ///
        /// Used by aggregator inside setup
        pub async fn get_params(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::OperatorParams>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/GetParams",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineOperator", "GetParams"));
            self.inner.server_streaming(req, path, codec).await
        }
        /// Returns an operator's deposit keys.
        /// Deposit keys include Assert BitVM winternitz keys, and challenge ACK hashes.
        ///
        /// Used by aggregator inside new_deposit
        pub async fn get_deposit_keys(
            &mut self,
            request: impl tonic::IntoRequest<super::DepositParams>,
        ) -> std::result::Result<tonic::Response<super::OperatorKeys>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/GetDepositKeys",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineOperator", "GetDepositKeys"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns the current status of tasks running on the operator and their last synced heights.
        pub async fn get_current_status(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::EntityStatus>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/GetCurrentStatus",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineOperator", "GetCurrentStatus"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Signs everything that includes Operator's burn connector.
        ///
        /// # Parameters
        ///
        /// - User's deposit information
        /// - Nonce metadata
        ///
        /// # Returns
        ///
        /// - Operator burn Schnorr signature
        pub async fn deposit_sign(
            &mut self,
            request: impl tonic::IntoRequest<super::DepositSignSession>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::SchnorrSig>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/DepositSign",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineOperator", "DepositSign"));
            self.inner.server_streaming(req, path, codec).await
        }
        /// Restarts the background tasks for the operator.
        pub async fn restart_background_tasks(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/RestartBackgroundTasks",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineOperator",
                        "RestartBackgroundTasks",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
        /// If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
        /// If automation is disabled, the withdrawal will not be accepted and an error will be returned.
        /// Note: This is intended for operator's own use, so it doesn't include a signature from aggregator.
        pub async fn internal_withdraw(
            &mut self,
            request: impl tonic::IntoRequest<super::WithdrawParams>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/InternalWithdraw",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineOperator", "InternalWithdraw"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// First, if verification address in operator's config is set, the signature in rpc is checked to see if it was signed by the verification address.
        /// Then prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
        /// If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
        /// If automation is disabled, the withdrawal will not be accepted and an error will be returned.
        pub async fn withdraw(
            &mut self,
            request: impl tonic::IntoRequest<super::WithdrawParamsWithSig>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/Withdraw",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineOperator", "Withdraw"));
            self.inner.unary(req, path, codec).await
        }
        /// For a given deposit outpoint, determines the next step in the kickoff process the operator is in,
        /// and returns the raw signed txs that the operator needs to send next, for enabling reimbursement process
        /// without automation.
        ///
        /// # Parameters
        /// - deposit_outpoint: Deposit outpoint to create the kickoff for
        ///
        /// # Returns
        /// - Raw signed txs that the operator needs to send next
        pub async fn get_reimbursement_txs(
            &mut self,
            request: impl tonic::IntoRequest<super::Outpoint>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/GetReimbursementTxs",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineOperator",
                        "GetReimbursementTxs",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
        /// Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
        /// It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
        ///
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - transaction_type: Requested Transaction type
        /// - kickoff_id: Operator's kickoff ID
        ///
        /// # Returns
        /// - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
        ///
        /// Only used in tests
        pub async fn internal_create_signed_txs(
            &mut self,
            request: impl tonic::IntoRequest<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/InternalCreateSignedTxs",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineOperator",
                        "InternalCreateSignedTxs",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Creates all assert transactions (AssertBegin, MiniAsserts, AssertEnd), signs them, and returns the raw txs
        /// in the same order.
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - kickoff_id: Operator's kickoff ID
        /// - commit_data: Commitment data for each MiniAssert tx's
        ///
        /// # Returns
        /// - Raw signed assert transactions
        pub async fn internal_create_assert_commitment_txs(
            &mut self,
            request: impl tonic::IntoRequest<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/InternalCreateAssertCommitmentTxs",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineOperator",
                        "InternalCreateAssertCommitmentTxs",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn internal_finalized_payout(
            &mut self,
            request: impl tonic::IntoRequest<super::FinalizedPayoutParams>,
        ) -> std::result::Result<tonic::Response<super::Txid>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/InternalFinalizedPayout",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineOperator",
                        "InternalFinalizedPayout",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn internal_end_round(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/InternalEndRound",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineOperator", "InternalEndRound"),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn vergen(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineOperator/Vergen",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineOperator", "Vergen"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated client implementations.
pub mod clementine_verifier_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct ClementineVerifierClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ClementineVerifierClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ClementineVerifierClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ClementineVerifierClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ClementineVerifierClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        /// Returns verifiers' metadata. Needs to be called once per setup.
        ///
        /// Used by aggregator inside setup to let all verifiers know all other verifier pks
        pub async fn get_params(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VerifierParams>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/GetParams",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "GetParams"));
            self.inner.unary(req, path, codec).await
        }
        /// Saves an operator.
        ///
        /// Used by aggregator inside setup to let all verifiers know all other operator pks
        pub async fn set_operator(
            &mut self,
            request: impl tonic::IntoStreamingRequest<Message = super::OperatorParams>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/SetOperator",
            );
            let mut req = request.into_streaming_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "SetOperator"));
            self.inner.client_streaming(req, path, codec).await
        }
        /// Sets the operator's winternitz keys and challenge ACK hashes and saves them
        /// into the db.
        ///
        /// Used by aggregator inside new_deposit to let all verifiers know all other operators' deposit information
        pub async fn set_operator_keys(
            &mut self,
            request: impl tonic::IntoRequest<super::OperatorKeysWithDeposit>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/SetOperatorKeys",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineVerifier", "SetOperatorKeys"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Generates nonces for a deposit.
        ///
        /// # Returns
        ///
        /// Nonce metadata followed by nonces.
        ///
        /// Used by aggregator inside new_deposit
        pub async fn nonce_gen(
            &mut self,
            request: impl tonic::IntoRequest<super::NonceGenRequest>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::NonceGenResponse>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/NonceGen",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "NonceGen"));
            self.inner.server_streaming(req, path, codec).await
        }
        /// Signs deposit with given aggNonces and verifier's secNonce using
        /// nonce_id.
        ///
        /// Used by aggregator inside new_deposit
        pub async fn deposit_sign(
            &mut self,
            request: impl tonic::IntoStreamingRequest<
                Message = super::VerifierDepositSignParams,
            >,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::PartialSig>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/DepositSign",
            );
            let mut req = request.into_streaming_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "DepositSign"));
            self.inner.streaming(req, path, codec).await
        }
        /// Signs the optimistic payout tx with given aggNonce and withdrawal info.
        pub async fn optimistic_payout_sign(
            &mut self,
            request: impl tonic::IntoRequest<super::OptimisticPayoutParams>,
        ) -> std::result::Result<tonic::Response<super::PartialSig>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/OptimisticPayoutSign",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineVerifier",
                        "OptimisticPayoutSign",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Verifies every signature and signs move_tx.
        ///
        /// Used by aggregator inside new_deposit
        pub async fn deposit_finalize(
            &mut self,
            request: impl tonic::IntoStreamingRequest<
                Message = super::VerifierDepositFinalizeParams,
            >,
        ) -> std::result::Result<
            tonic::Response<super::VerifierDepositFinalizeResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/DepositFinalize",
            );
            let mut req = request.into_streaming_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineVerifier", "DepositFinalize"),
                );
            self.inner.client_streaming(req, path, codec).await
        }
        /// Debug a transaction by retrieving its current state and history
        pub async fn debug_tx(
            &mut self,
            request: impl tonic::IntoRequest<super::TxDebugRequest>,
        ) -> std::result::Result<tonic::Response<super::TxDebugInfo>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/DebugTx",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "DebugTx"));
            self.inner.unary(req, path, codec).await
        }
        /// Restarts the background tasks for the verifier.
        pub async fn restart_background_tasks(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/RestartBackgroundTasks",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineVerifier",
                        "RestartBackgroundTasks",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Checks if the kickoff tx is malicious and if so, try to send all necessary txs to punish the operator
        pub async fn internal_handle_kickoff(
            &mut self,
            request: impl tonic::IntoRequest<super::Txid>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/InternalHandleKickoff",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineVerifier",
                        "InternalHandleKickoff",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns the current status of tasks running on the verifier and their last synced heights.
        pub async fn get_current_status(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::EntityStatus>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/GetCurrentStatus",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineVerifier", "GetCurrentStatus"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// 1. Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
        /// 2. Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
        /// 3. It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
        ///
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - transaction_type: Requested Transaction type
        /// - kickoff_id: Operator's kickoff ID
        ///
        /// # Returns
        /// - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
        pub async fn internal_create_signed_txs(
            &mut self,
            request: impl tonic::IntoRequest<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/InternalCreateSignedTxs",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineVerifier",
                        "InternalCreateSignedTxs",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Signs the verifiers own watchtower challenge tx in the corresponding
        /// kickoff and returns the signed raw tx
        pub async fn internal_create_watchtower_challenge(
            &mut self,
            request: impl tonic::IntoRequest<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RawTxWithRbfInfo>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/InternalCreateWatchtowerChallenge",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineVerifier",
                        "InternalCreateWatchtowerChallenge",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn vergen(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineVerifier/Vergen",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineVerifier", "Vergen"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated client implementations.
pub mod clementine_aggregator_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct ClementineAggregatorClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ClementineAggregatorClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ClementineAggregatorClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ClementineAggregatorClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ClementineAggregatorClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn get_nofn_aggregated_xonly_pk(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::NofnResponse>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/GetNofnAggregatedXonlyPk",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineAggregator",
                        "GetNofnAggregatedXonlyPk",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Sets up the system of verifiers, watchtowers and operators by:
        ///
        /// 1. Collects verifier keys from each verifier
        /// 2. Distributes these verifier keys to all verifiers
        /// 3. Collects all operator configs from each operator
        /// 4. Distributes these operator configs to all verifiers
        ///
        /// Used by the clementine-backend service
        pub async fn setup(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<
            tonic::Response<super::VerifierPublicKeys>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/Setup",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineAggregator", "Setup"));
            self.inner.unary(req, path, codec).await
        }
        /// This will call, DepositNonceGen for every verifier,
        /// then it will aggregate one by one and then send it to DepositSign,
        /// then it will aggregate the partial sigs and send it to DepositFinalize,
        /// this will also call the operator to get their signatures and send it to
        /// DepositFinalize then it will collect the partial sigs and create the move
        /// tx.
        ///
        /// Used by the clementine-backend service to initiate a deposit
        pub async fn new_deposit(
            &mut self,
            request: impl tonic::IntoRequest<super::Deposit>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/NewDeposit",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineAggregator", "NewDeposit"),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Call's withdraw on all operators
        /// Used by the clementine-backend service to initiate a withdrawal
        /// If the operator's xonly public keys list is empty, the withdrawal will be sent to all operators.
        /// If not, only the operators in the list will be sent the withdrawal request.
        pub async fn withdraw(
            &mut self,
            request: impl tonic::IntoRequest<super::AggregatorWithdrawalInput>,
        ) -> std::result::Result<
            tonic::Response<super::AggregatorWithdrawResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/Withdraw",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineAggregator", "Withdraw"));
            self.inner.unary(req, path, codec).await
        }
        /// Perform an optimistic payout to reimburse a peg-out from Citrea
        pub async fn optimistic_payout(
            &mut self,
            request: impl tonic::IntoRequest<super::OptimisticWithdrawParams>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/OptimisticPayout",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineAggregator",
                        "OptimisticPayout",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Send a pre-signed tx to the network
        pub async fn internal_send_tx(
            &mut self,
            request: impl tonic::IntoRequest<super::SendTxRequest>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/InternalSendTx",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("clementine.ClementineAggregator", "InternalSendTx"),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn send_move_to_vault_tx(
            &mut self,
            request: impl tonic::IntoRequest<super::SendMoveTxRequest>,
        ) -> std::result::Result<tonic::Response<super::Txid>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/SendMoveToVaultTx",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineAggregator",
                        "SendMoveToVaultTx",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Returns the current status of tasks running on the operators/verifiers.
        /// If restart_tasks is true, it will restart the tasks on the entities if they are stopped.
        pub async fn get_entity_statuses(
            &mut self,
            request: impl tonic::IntoRequest<super::GetEntityStatusesRequest>,
        ) -> std::result::Result<tonic::Response<super::EntityStatuses>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/GetEntityStatuses",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineAggregator",
                        "GetEntityStatuses",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// Creates an emergency stop tx that won't be broadcasted.
        /// Tx will have around 3 sats/vbyte fee.
        /// Set add_anchor to true to add an anchor output for cpfp..
        pub async fn internal_get_emergency_stop_tx(
            &mut self,
            request: impl tonic::IntoRequest<super::GetEmergencyStopTxRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetEmergencyStopTxResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/InternalGetEmergencyStopTx",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "clementine.ClementineAggregator",
                        "InternalGetEmergencyStopTx",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn vergen(
            &mut self,
            request: impl tonic::IntoRequest<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/clementine.ClementineAggregator/Vergen",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("clementine.ClementineAggregator", "Vergen"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod clementine_operator_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ClementineOperatorServer.
    #[async_trait]
    pub trait ClementineOperator: std::marker::Send + std::marker::Sync + 'static {
        /// Returns the operator's xonly public key
        ///
        /// Used by aggregator inside setup
        async fn get_x_only_public_key(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<
            tonic::Response<super::XOnlyPublicKeyRpc>,
            tonic::Status,
        >;
        /// Server streaming response type for the GetParams method.
        type GetParamsStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::OperatorParams, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        /// Returns an operator's parameters. It will be called once, by the
        /// aggregator, to set all the public keys.
        ///
        /// # Returns
        ///
        /// Returns an [`OperatorParams`], which includes operator's configuration and
        /// Watchtower parameters.
        ///
        /// Used by aggregator inside setup
        async fn get_params(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<Self::GetParamsStream>, tonic::Status>;
        /// Returns an operator's deposit keys.
        /// Deposit keys include Assert BitVM winternitz keys, and challenge ACK hashes.
        ///
        /// Used by aggregator inside new_deposit
        async fn get_deposit_keys(
            &self,
            request: tonic::Request<super::DepositParams>,
        ) -> std::result::Result<tonic::Response<super::OperatorKeys>, tonic::Status>;
        /// Returns the current status of tasks running on the operator and their last synced heights.
        async fn get_current_status(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::EntityStatus>, tonic::Status>;
        /// Server streaming response type for the DepositSign method.
        type DepositSignStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::SchnorrSig, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        /// Signs everything that includes Operator's burn connector.
        ///
        /// # Parameters
        ///
        /// - User's deposit information
        /// - Nonce metadata
        ///
        /// # Returns
        ///
        /// - Operator burn Schnorr signature
        async fn deposit_sign(
            &self,
            request: tonic::Request<super::DepositSignSession>,
        ) -> std::result::Result<
            tonic::Response<Self::DepositSignStream>,
            tonic::Status,
        >;
        /// Restarts the background tasks for the operator.
        async fn restart_background_tasks(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        /// Prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
        /// If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
        /// If automation is disabled, the withdrawal will not be accepted and an error will be returned.
        /// Note: This is intended for operator's own use, so it doesn't include a signature from aggregator.
        async fn internal_withdraw(
            &self,
            request: tonic::Request<super::WithdrawParams>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status>;
        /// First, if verification address in operator's config is set, the signature in rpc is checked to see if it was signed by the verification address.
        /// Then prepares a withdrawal if it's profitable and the withdrawal is correct and registered in Citrea bridge contract.
        /// If withdrawal is accepted, the payout tx will be added to the TxSender and success is returned, otherwise an error is returned.
        /// If automation is disabled, the withdrawal will not be accepted and an error will be returned.
        async fn withdraw(
            &self,
            request: tonic::Request<super::WithdrawParamsWithSig>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status>;
        /// For a given deposit outpoint, determines the next step in the kickoff process the operator is in,
        /// and returns the raw signed txs that the operator needs to send next, for enabling reimbursement process
        /// without automation.
        ///
        /// # Parameters
        /// - deposit_outpoint: Deposit outpoint to create the kickoff for
        ///
        /// # Returns
        /// - Raw signed txs that the operator needs to send next
        async fn get_reimbursement_txs(
            &self,
            request: tonic::Request<super::Outpoint>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        >;
        /// Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
        /// Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
        /// It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
        ///
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - transaction_type: Requested Transaction type
        /// - kickoff_id: Operator's kickoff ID
        ///
        /// # Returns
        /// - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
        ///
        /// Only used in tests
        async fn internal_create_signed_txs(
            &self,
            request: tonic::Request<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        >;
        /// Creates all assert transactions (AssertBegin, MiniAsserts, AssertEnd), signs them, and returns the raw txs
        /// in the same order.
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - kickoff_id: Operator's kickoff ID
        /// - commit_data: Commitment data for each MiniAssert tx's
        ///
        /// # Returns
        /// - Raw signed assert transactions
        async fn internal_create_assert_commitment_txs(
            &self,
            request: tonic::Request<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        >;
        async fn internal_finalized_payout(
            &self,
            request: tonic::Request<super::FinalizedPayoutParams>,
        ) -> std::result::Result<tonic::Response<super::Txid>, tonic::Status>;
        async fn internal_end_round(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        async fn vergen(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status>;
    }
    /// An operator is responsible for paying withdrawals. It has an unique ID and
    /// chain of UTXOs named `round_txs`. An operator also runs a verifier. These are
    /// connected to the same database and both have access to watchtowers'
    /// winternitz pubkeys.
    #[derive(Debug)]
    pub struct ClementineOperatorServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ClementineOperatorServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ClementineOperatorServer<T>
    where
        T: ClementineOperator,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/clementine.ClementineOperator/GetXOnlyPublicKey" => {
                    #[allow(non_camel_case_types)]
                    struct GetXOnlyPublicKeySvc<T: ClementineOperator>(pub Arc<T>);
                    impl<T: ClementineOperator> tonic::server::UnaryService<super::Empty>
                    for GetXOnlyPublicKeySvc<T> {
                        type Response = super::XOnlyPublicKeyRpc;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::get_x_only_public_key(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetXOnlyPublicKeySvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/GetParams" => {
                    #[allow(non_camel_case_types)]
                    struct GetParamsSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::ServerStreamingService<super::Empty>
                    for GetParamsSvc<T> {
                        type Response = super::OperatorParams;
                        type ResponseStream = T::GetParamsStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::get_params(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetParamsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.server_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/GetDepositKeys" => {
                    #[allow(non_camel_case_types)]
                    struct GetDepositKeysSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::DepositParams>
                    for GetDepositKeysSvc<T> {
                        type Response = super::OperatorKeys;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DepositParams>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::get_deposit_keys(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetDepositKeysSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/GetCurrentStatus" => {
                    #[allow(non_camel_case_types)]
                    struct GetCurrentStatusSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<T: ClementineOperator> tonic::server::UnaryService<super::Empty>
                    for GetCurrentStatusSvc<T> {
                        type Response = super::EntityStatus;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::get_current_status(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetCurrentStatusSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/DepositSign" => {
                    #[allow(non_camel_case_types)]
                    struct DepositSignSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::ServerStreamingService<super::DepositSignSession>
                    for DepositSignSvc<T> {
                        type Response = super::SchnorrSig;
                        type ResponseStream = T::DepositSignStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DepositSignSession>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::deposit_sign(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DepositSignSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.server_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/RestartBackgroundTasks" => {
                    #[allow(non_camel_case_types)]
                    struct RestartBackgroundTasksSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<T: ClementineOperator> tonic::server::UnaryService<super::Empty>
                    for RestartBackgroundTasksSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::restart_background_tasks(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = RestartBackgroundTasksSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/InternalWithdraw" => {
                    #[allow(non_camel_case_types)]
                    struct InternalWithdrawSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::WithdrawParams>
                    for InternalWithdrawSvc<T> {
                        type Response = super::RawSignedTx;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::WithdrawParams>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::internal_withdraw(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalWithdrawSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/Withdraw" => {
                    #[allow(non_camel_case_types)]
                    struct WithdrawSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::WithdrawParamsWithSig>
                    for WithdrawSvc<T> {
                        type Response = super::RawSignedTx;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::WithdrawParamsWithSig>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::withdraw(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = WithdrawSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/GetReimbursementTxs" => {
                    #[allow(non_camel_case_types)]
                    struct GetReimbursementTxsSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::Outpoint>
                    for GetReimbursementTxsSvc<T> {
                        type Response = super::SignedTxsWithType;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Outpoint>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::get_reimbursement_txs(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetReimbursementTxsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/InternalCreateSignedTxs" => {
                    #[allow(non_camel_case_types)]
                    struct InternalCreateSignedTxsSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::TransactionRequest>
                    for InternalCreateSignedTxsSvc<T> {
                        type Response = super::SignedTxsWithType;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TransactionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::internal_create_signed_txs(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalCreateSignedTxsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/InternalCreateAssertCommitmentTxs" => {
                    #[allow(non_camel_case_types)]
                    struct InternalCreateAssertCommitmentTxsSvc<T: ClementineOperator>(
                        pub Arc<T>,
                    );
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::TransactionRequest>
                    for InternalCreateAssertCommitmentTxsSvc<T> {
                        type Response = super::SignedTxsWithType;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TransactionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::internal_create_assert_commitment_txs(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalCreateAssertCommitmentTxsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/InternalFinalizedPayout" => {
                    #[allow(non_camel_case_types)]
                    struct InternalFinalizedPayoutSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<
                        T: ClementineOperator,
                    > tonic::server::UnaryService<super::FinalizedPayoutParams>
                    for InternalFinalizedPayoutSvc<T> {
                        type Response = super::Txid;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::FinalizedPayoutParams>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::internal_finalized_payout(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalFinalizedPayoutSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/InternalEndRound" => {
                    #[allow(non_camel_case_types)]
                    struct InternalEndRoundSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<T: ClementineOperator> tonic::server::UnaryService<super::Empty>
                    for InternalEndRoundSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::internal_end_round(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalEndRoundSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineOperator/Vergen" => {
                    #[allow(non_camel_case_types)]
                    struct VergenSvc<T: ClementineOperator>(pub Arc<T>);
                    impl<T: ClementineOperator> tonic::server::UnaryService<super::Empty>
                    for VergenSvc<T> {
                        type Response = super::VergenResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineOperator>::vergen(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = VergenSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ClementineOperatorServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "clementine.ClementineOperator";
    impl<T> tonic::server::NamedService for ClementineOperatorServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
/// Generated server implementations.
pub mod clementine_verifier_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ClementineVerifierServer.
    #[async_trait]
    pub trait ClementineVerifier: std::marker::Send + std::marker::Sync + 'static {
        /// Returns verifiers' metadata. Needs to be called once per setup.
        ///
        /// Used by aggregator inside setup to let all verifiers know all other verifier pks
        async fn get_params(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VerifierParams>, tonic::Status>;
        /// Saves an operator.
        ///
        /// Used by aggregator inside setup to let all verifiers know all other operator pks
        async fn set_operator(
            &self,
            request: tonic::Request<tonic::Streaming<super::OperatorParams>>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        /// Sets the operator's winternitz keys and challenge ACK hashes and saves them
        /// into the db.
        ///
        /// Used by aggregator inside new_deposit to let all verifiers know all other operators' deposit information
        async fn set_operator_keys(
            &self,
            request: tonic::Request<super::OperatorKeysWithDeposit>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        /// Server streaming response type for the NonceGen method.
        type NonceGenStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::NonceGenResponse, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        /// Generates nonces for a deposit.
        ///
        /// # Returns
        ///
        /// Nonce metadata followed by nonces.
        ///
        /// Used by aggregator inside new_deposit
        async fn nonce_gen(
            &self,
            request: tonic::Request<super::NonceGenRequest>,
        ) -> std::result::Result<tonic::Response<Self::NonceGenStream>, tonic::Status>;
        /// Server streaming response type for the DepositSign method.
        type DepositSignStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::PartialSig, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        /// Signs deposit with given aggNonces and verifier's secNonce using
        /// nonce_id.
        ///
        /// Used by aggregator inside new_deposit
        async fn deposit_sign(
            &self,
            request: tonic::Request<tonic::Streaming<super::VerifierDepositSignParams>>,
        ) -> std::result::Result<
            tonic::Response<Self::DepositSignStream>,
            tonic::Status,
        >;
        /// Signs the optimistic payout tx with given aggNonce and withdrawal info.
        async fn optimistic_payout_sign(
            &self,
            request: tonic::Request<super::OptimisticPayoutParams>,
        ) -> std::result::Result<tonic::Response<super::PartialSig>, tonic::Status>;
        /// Verifies every signature and signs move_tx.
        ///
        /// Used by aggregator inside new_deposit
        async fn deposit_finalize(
            &self,
            request: tonic::Request<
                tonic::Streaming<super::VerifierDepositFinalizeParams>,
            >,
        ) -> std::result::Result<
            tonic::Response<super::VerifierDepositFinalizeResponse>,
            tonic::Status,
        >;
        /// Debug a transaction by retrieving its current state and history
        async fn debug_tx(
            &self,
            request: tonic::Request<super::TxDebugRequest>,
        ) -> std::result::Result<tonic::Response<super::TxDebugInfo>, tonic::Status>;
        /// Restarts the background tasks for the verifier.
        async fn restart_background_tasks(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        /// Checks if the kickoff tx is malicious and if so, try to send all necessary txs to punish the operator
        async fn internal_handle_kickoff(
            &self,
            request: tonic::Request<super::Txid>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        /// Returns the current status of tasks running on the verifier and their last synced heights.
        async fn get_current_status(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::EntityStatus>, tonic::Status>;
        /// 1. Signs all tx's it can according to given transaction type (use it with AllNeededForDeposit to get almost all tx's)
        /// 2. Creates the transactions denoted by the deposit and operator_idx, round_idx, and kickoff_idx.
        /// 3. It will create the transaction and sign it with the operator's private key and/or saved nofn signatures.
        ///
        /// # Parameters
        /// - deposit_params: User's deposit information
        /// - transaction_type: Requested Transaction type
        /// - kickoff_id: Operator's kickoff ID
        ///
        /// # Returns
        /// - Raw signed transactions that the entity can sign (no asserts and watchtower challenge)
        async fn internal_create_signed_txs(
            &self,
            request: tonic::Request<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::SignedTxsWithType>,
            tonic::Status,
        >;
        /// Signs the verifiers own watchtower challenge tx in the corresponding
        /// kickoff and returns the signed raw tx
        async fn internal_create_watchtower_challenge(
            &self,
            request: tonic::Request<super::TransactionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RawTxWithRbfInfo>,
            tonic::Status,
        >;
        async fn vergen(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status>;
    }
    #[derive(Debug)]
    pub struct ClementineVerifierServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ClementineVerifierServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ClementineVerifierServer<T>
    where
        T: ClementineVerifier,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/clementine.ClementineVerifier/GetParams" => {
                    #[allow(non_camel_case_types)]
                    struct GetParamsSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<T: ClementineVerifier> tonic::server::UnaryService<super::Empty>
                    for GetParamsSvc<T> {
                        type Response = super::VerifierParams;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::get_params(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetParamsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/SetOperator" => {
                    #[allow(non_camel_case_types)]
                    struct SetOperatorSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::ClientStreamingService<super::OperatorParams>
                    for SetOperatorSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<
                                tonic::Streaming<super::OperatorParams>,
                            >,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::set_operator(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SetOperatorSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.client_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/SetOperatorKeys" => {
                    #[allow(non_camel_case_types)]
                    struct SetOperatorKeysSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::UnaryService<super::OperatorKeysWithDeposit>
                    for SetOperatorKeysSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::OperatorKeysWithDeposit>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::set_operator_keys(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SetOperatorKeysSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/NonceGen" => {
                    #[allow(non_camel_case_types)]
                    struct NonceGenSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::ServerStreamingService<super::NonceGenRequest>
                    for NonceGenSvc<T> {
                        type Response = super::NonceGenResponse;
                        type ResponseStream = T::NonceGenStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::NonceGenRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::nonce_gen(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = NonceGenSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.server_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/DepositSign" => {
                    #[allow(non_camel_case_types)]
                    struct DepositSignSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::StreamingService<super::VerifierDepositSignParams>
                    for DepositSignSvc<T> {
                        type Response = super::PartialSig;
                        type ResponseStream = T::DepositSignStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<
                                tonic::Streaming<super::VerifierDepositSignParams>,
                            >,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::deposit_sign(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DepositSignSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/OptimisticPayoutSign" => {
                    #[allow(non_camel_case_types)]
                    struct OptimisticPayoutSignSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::UnaryService<super::OptimisticPayoutParams>
                    for OptimisticPayoutSignSvc<T> {
                        type Response = super::PartialSig;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::OptimisticPayoutParams>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::optimistic_payout_sign(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = OptimisticPayoutSignSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/DepositFinalize" => {
                    #[allow(non_camel_case_types)]
                    struct DepositFinalizeSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::ClientStreamingService<
                        super::VerifierDepositFinalizeParams,
                    > for DepositFinalizeSvc<T> {
                        type Response = super::VerifierDepositFinalizeResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<
                                tonic::Streaming<super::VerifierDepositFinalizeParams>,
                            >,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::deposit_finalize(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DepositFinalizeSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.client_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/DebugTx" => {
                    #[allow(non_camel_case_types)]
                    struct DebugTxSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::UnaryService<super::TxDebugRequest>
                    for DebugTxSvc<T> {
                        type Response = super::TxDebugInfo;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TxDebugRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::debug_tx(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DebugTxSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/RestartBackgroundTasks" => {
                    #[allow(non_camel_case_types)]
                    struct RestartBackgroundTasksSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<T: ClementineVerifier> tonic::server::UnaryService<super::Empty>
                    for RestartBackgroundTasksSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::restart_background_tasks(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = RestartBackgroundTasksSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/InternalHandleKickoff" => {
                    #[allow(non_camel_case_types)]
                    struct InternalHandleKickoffSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<T: ClementineVerifier> tonic::server::UnaryService<super::Txid>
                    for InternalHandleKickoffSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Txid>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::internal_handle_kickoff(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalHandleKickoffSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/GetCurrentStatus" => {
                    #[allow(non_camel_case_types)]
                    struct GetCurrentStatusSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<T: ClementineVerifier> tonic::server::UnaryService<super::Empty>
                    for GetCurrentStatusSvc<T> {
                        type Response = super::EntityStatus;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::get_current_status(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetCurrentStatusSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/InternalCreateSignedTxs" => {
                    #[allow(non_camel_case_types)]
                    struct InternalCreateSignedTxsSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::UnaryService<super::TransactionRequest>
                    for InternalCreateSignedTxsSvc<T> {
                        type Response = super::SignedTxsWithType;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TransactionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::internal_create_signed_txs(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalCreateSignedTxsSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/InternalCreateWatchtowerChallenge" => {
                    #[allow(non_camel_case_types)]
                    struct InternalCreateWatchtowerChallengeSvc<T: ClementineVerifier>(
                        pub Arc<T>,
                    );
                    impl<
                        T: ClementineVerifier,
                    > tonic::server::UnaryService<super::TransactionRequest>
                    for InternalCreateWatchtowerChallengeSvc<T> {
                        type Response = super::RawTxWithRbfInfo;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TransactionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::internal_create_watchtower_challenge(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalCreateWatchtowerChallengeSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineVerifier/Vergen" => {
                    #[allow(non_camel_case_types)]
                    struct VergenSvc<T: ClementineVerifier>(pub Arc<T>);
                    impl<T: ClementineVerifier> tonic::server::UnaryService<super::Empty>
                    for VergenSvc<T> {
                        type Response = super::VergenResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineVerifier>::vergen(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = VergenSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ClementineVerifierServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "clementine.ClementineVerifier";
    impl<T> tonic::server::NamedService for ClementineVerifierServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
/// Generated server implementations.
pub mod clementine_aggregator_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ClementineAggregatorServer.
    #[async_trait]
    pub trait ClementineAggregator: std::marker::Send + std::marker::Sync + 'static {
        async fn get_nofn_aggregated_xonly_pk(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::NofnResponse>, tonic::Status>;
        /// Sets up the system of verifiers, watchtowers and operators by:
        ///
        /// 1. Collects verifier keys from each verifier
        /// 2. Distributes these verifier keys to all verifiers
        /// 3. Collects all operator configs from each operator
        /// 4. Distributes these operator configs to all verifiers
        ///
        /// Used by the clementine-backend service
        async fn setup(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<
            tonic::Response<super::VerifierPublicKeys>,
            tonic::Status,
        >;
        /// This will call, DepositNonceGen for every verifier,
        /// then it will aggregate one by one and then send it to DepositSign,
        /// then it will aggregate the partial sigs and send it to DepositFinalize,
        /// this will also call the operator to get their signatures and send it to
        /// DepositFinalize then it will collect the partial sigs and create the move
        /// tx.
        ///
        /// Used by the clementine-backend service to initiate a deposit
        async fn new_deposit(
            &self,
            request: tonic::Request<super::Deposit>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status>;
        /// Call's withdraw on all operators
        /// Used by the clementine-backend service to initiate a withdrawal
        /// If the operator's xonly public keys list is empty, the withdrawal will be sent to all operators.
        /// If not, only the operators in the list will be sent the withdrawal request.
        async fn withdraw(
            &self,
            request: tonic::Request<super::AggregatorWithdrawalInput>,
        ) -> std::result::Result<
            tonic::Response<super::AggregatorWithdrawResponse>,
            tonic::Status,
        >;
        /// Perform an optimistic payout to reimburse a peg-out from Citrea
        async fn optimistic_payout(
            &self,
            request: tonic::Request<super::OptimisticWithdrawParams>,
        ) -> std::result::Result<tonic::Response<super::RawSignedTx>, tonic::Status>;
        /// Send a pre-signed tx to the network
        async fn internal_send_tx(
            &self,
            request: tonic::Request<super::SendTxRequest>,
        ) -> std::result::Result<tonic::Response<super::Empty>, tonic::Status>;
        async fn send_move_to_vault_tx(
            &self,
            request: tonic::Request<super::SendMoveTxRequest>,
        ) -> std::result::Result<tonic::Response<super::Txid>, tonic::Status>;
        /// Returns the current status of tasks running on the operators/verifiers.
        /// If restart_tasks is true, it will restart the tasks on the entities if they are stopped.
        async fn get_entity_statuses(
            &self,
            request: tonic::Request<super::GetEntityStatusesRequest>,
        ) -> std::result::Result<tonic::Response<super::EntityStatuses>, tonic::Status>;
        /// Creates an emergency stop tx that won't be broadcasted.
        /// Tx will have around 3 sats/vbyte fee.
        /// Set add_anchor to true to add an anchor output for cpfp..
        async fn internal_get_emergency_stop_tx(
            &self,
            request: tonic::Request<super::GetEmergencyStopTxRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetEmergencyStopTxResponse>,
            tonic::Status,
        >;
        async fn vergen(
            &self,
            request: tonic::Request<super::Empty>,
        ) -> std::result::Result<tonic::Response<super::VergenResponse>, tonic::Status>;
    }
    #[derive(Debug)]
    pub struct ClementineAggregatorServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ClementineAggregatorServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>>
    for ClementineAggregatorServer<T>
    where
        T: ClementineAggregator,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/clementine.ClementineAggregator/GetNofnAggregatedXonlyPk" => {
                    #[allow(non_camel_case_types)]
                    struct GetNofnAggregatedXonlyPkSvc<T: ClementineAggregator>(
                        pub Arc<T>,
                    );
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::Empty>
                    for GetNofnAggregatedXonlyPkSvc<T> {
                        type Response = super::NofnResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::get_nofn_aggregated_xonly_pk(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetNofnAggregatedXonlyPkSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/Setup" => {
                    #[allow(non_camel_case_types)]
                    struct SetupSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::Empty> for SetupSvc<T> {
                        type Response = super::VerifierPublicKeys;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::setup(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SetupSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/NewDeposit" => {
                    #[allow(non_camel_case_types)]
                    struct NewDepositSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::Deposit> for NewDepositSvc<T> {
                        type Response = super::RawSignedTx;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Deposit>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::new_deposit(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = NewDepositSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/Withdraw" => {
                    #[allow(non_camel_case_types)]
                    struct WithdrawSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::AggregatorWithdrawalInput>
                    for WithdrawSvc<T> {
                        type Response = super::AggregatorWithdrawResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::AggregatorWithdrawalInput>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::withdraw(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = WithdrawSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/OptimisticPayout" => {
                    #[allow(non_camel_case_types)]
                    struct OptimisticPayoutSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::OptimisticWithdrawParams>
                    for OptimisticPayoutSvc<T> {
                        type Response = super::RawSignedTx;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::OptimisticWithdrawParams>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::optimistic_payout(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = OptimisticPayoutSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/InternalSendTx" => {
                    #[allow(non_camel_case_types)]
                    struct InternalSendTxSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::SendTxRequest>
                    for InternalSendTxSvc<T> {
                        type Response = super::Empty;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::SendTxRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::internal_send_tx(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalSendTxSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/SendMoveToVaultTx" => {
                    #[allow(non_camel_case_types)]
                    struct SendMoveToVaultTxSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::SendMoveTxRequest>
                    for SendMoveToVaultTxSvc<T> {
                        type Response = super::Txid;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::SendMoveTxRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::send_move_to_vault_tx(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SendMoveToVaultTxSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/GetEntityStatuses" => {
                    #[allow(non_camel_case_types)]
                    struct GetEntityStatusesSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::GetEntityStatusesRequest>
                    for GetEntityStatusesSvc<T> {
                        type Response = super::EntityStatuses;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetEntityStatusesRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::get_entity_statuses(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetEntityStatusesSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/InternalGetEmergencyStopTx" => {
                    #[allow(non_camel_case_types)]
                    struct InternalGetEmergencyStopTxSvc<T: ClementineAggregator>(
                        pub Arc<T>,
                    );
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::GetEmergencyStopTxRequest>
                    for InternalGetEmergencyStopTxSvc<T> {
                        type Response = super::GetEmergencyStopTxResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetEmergencyStopTxRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::internal_get_emergency_stop_tx(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = InternalGetEmergencyStopTxSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/clementine.ClementineAggregator/Vergen" => {
                    #[allow(non_camel_case_types)]
                    struct VergenSvc<T: ClementineAggregator>(pub Arc<T>);
                    impl<
                        T: ClementineAggregator,
                    > tonic::server::UnaryService<super::Empty> for VergenSvc<T> {
                        type Response = super::VergenResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::Empty>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ClementineAggregator>::vergen(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = VergenSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ClementineAggregatorServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "clementine.ClementineAggregator";
    impl<T> tonic::server::NamedService for ClementineAggregatorServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
