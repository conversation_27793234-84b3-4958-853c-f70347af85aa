[package]
name = "work-only-guest"
version = "0.1.0"
edition = "2021"

[workspace]

[dependencies]
circuits-lib = { path = "../../../circuits-lib" }

[patch.crates-io]
sha2 = { git = "https://github.com/risc0/RustCrypto-hashes", tag = "sha2-v0.10.8-risczero.0" }
crypto-bigint = { git = "https://github.com/risc0/RustCrypto-crypto-bigint", tag = "v0.5.5-risczero.0" }
k256 = { git = "https://github.com/risc0/RustCrypto-elliptic-curves", tag = "k256/v0.13.3-risczero.1" }

[profile.release]
debug = 0
lto = true
opt-level = 3
codegen-units = 1